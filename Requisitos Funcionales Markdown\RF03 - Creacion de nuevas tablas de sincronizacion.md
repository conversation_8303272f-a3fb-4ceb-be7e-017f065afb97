Como un: Administrador del Sistema / Operador
Quiero: un mecanismo robusto y automatizado para sincronizar los tickets de pesaje con el sistema externo Urbetrack
Para: poder asegurar la consistencia de los datos entre ambos sistemas, gestionar fallos de comunicación de forma automática y tener una visibilidad clara del estado de cada integración.


Resumen Técnico

Esta historia de usuario introduce la infraestructura necesaria para una sincronización controlada y resiliente de los Tickets de Pesaje (REPEM03_Pesajes_Balanza) con el sistema Urbetrack.

Para lograrlo, se crearán dos nuevas tablas en la base de datos:

 Reporting-Emvarias_XX-SincronizacionUrbetrack: Para rastrear el estado de sincronización de cada ticket de forma individual (relación 1 a 1).

 Reporting-Emvarias_XX-ParametriaTareasProgramadas: Para configurar y controlar la ejecución de los trabajos en segundo plano responsables de la sincronización.


Especificaciones Técnicas


1. Nueva Tabla: Reporting-Emvarias_XX-SincronizacionUrbetrack

Esta tabla mantendrá una relación uno a uno con REPEM03_Pesajes_Balanza y servirá como el log de estado para la integración de cada ticket.

![alt text](image.png)

2. Estados de Integración (Enum EstadoSincronizacion)

El campo Estado en la tabla SincronizacionUrbetrack almacenará los siguientes valores como texto.

![alt text](image-1.png)

1. Nueva Tabla:  Reporting-Emvarias_XX-ParametriaTareasProgramadas

Esta es una tabla genérica para configurar la ejecución de diferentes trabajos programados en el sistema.

![alt text](image-2.png)


Criterios de Aceptacion

1.  Creación de la Tabla de Sincronización. La tabla `Reporting-Emvarias_XX-SincronizacionUrbetrack` debe existir en la base de datos con la estructura y restricciones definidas.
2.  Creación de la Tabla de Parametrización. La tabla `Reporting-Emvarias_XX-ParametriaTareasProgramadas` debe existir en la base de datos para permitir la configuración de trabajos programados. No olvidar configurar los enums como tipo textuales para mayor descriptividad, en vez de almacenarlos de forma numerica.
3.  Disparador de Sincronización. Al crearse un nuevo registro en la tabla `REPEM03_Pesajes_Balanza`, se debe crear automáticamente un registro asociado en `SincronizacionUrbetrack` con los siguientes valores por defecto:
    *   `Estado`: 'PendienteDeCreacion'
    *   `NroReintentos`: 0
    *   `UltimaSincronizacion`: NULL
    *   `IdInternoUrbetrack`: NULL

