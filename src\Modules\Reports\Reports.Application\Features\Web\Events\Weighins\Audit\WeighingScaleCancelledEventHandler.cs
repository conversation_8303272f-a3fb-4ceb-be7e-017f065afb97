using MediatR;
using Reports.Domain;
using Reports.Domain.Constants;
using Reports.Domain.Entities;
using Reports.Domain.Events;

namespace Reports.Application.Features.Web.Events.Weighins.Audit;

public class WeighingScaleCancelledEventHandler : INotificationHandler<WeighingScaleCancelledEvent>
{
    private readonly IReportsUnitOfWork _unitOfWork;

    public WeighingScaleCancelledEventHandler(IReportsUnitOfWork unitOfWork)
    {
        _unitOfWork = unitOfWork;
    }

    public async Task Handle(WeighingScaleCancelledEvent notification, CancellationToken cancellationToken)
    {
        var auditRecords = HistoricalAudit
            .ForWeighingScales(notification.WeighingScales)
            .WithAction(HistoricalMovementType.Annulled)
            .ByUser(notification.User)
            .Build();

        await _unitOfWork.BeginTransactionAsync(cancellationToken);
        
        await _unitOfWork.HistoricalAudits.BulkInsertAsync(auditRecords, cancellationToken);
        
        await _unitOfWork.CommitAsync(cancellationToken);
    }
}
