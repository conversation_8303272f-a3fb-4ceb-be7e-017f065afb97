using Microsoft.Extensions.Logging;
using Reports.Application.Services.Http.Legacy;
using Reports.Domain;
using Reports.Domain.Constants;

namespace Reports.Application.Services.Synchronization.Executors;

/// <summary>
/// Job executor responsible for synchronizing deleted unloading tickets to Urbetrack.
/// Processes WeighingScale records with PendingCancellation status.
/// </summary>
public class SyncUnloadingTicketDeletionExecutor : BaseJobExecutor
{
    private readonly ILegacyApiService _legacyApiService;
    private const int MaxRetryCount = 3;

    public SyncUnloadingTicketDeletionExecutor(
        IReportsUnitOfWork unitOfWork,
        ILogger<SyncUnloadingTicketDeletionExecutor> logger,
        ILegacyApiService legacyApiService)
        : base(unitOfWork, logger)
    {
        _legacyApiService = legacyApiService;
    }

    protected override async Task ExecuteJobAsync(CancellationToken cancellationToken)
    {
        var pendingRecords = await GetSynchronizationRecordsAsync(
            SynchronizationStatus.PendingCancellation, 
            MaxRetryCount, 
            cancellationToken);

        if (!pendingRecords.Any())
        {
            Logger.LogInformation("No pending cancellation records found for synchronization");
            return;
        }

        Logger.LogInformation("Found {Count} pending cancellation records for synchronization", pendingRecords.Count);

        foreach (var syncRecord in pendingRecords)
        {
            await ProcessSyncRecordAsync(syncRecord, cancellationToken);
        }
    }

    private async Task ProcessSyncRecordAsync(Domain.Entities.UrbetrackSynchronization syncRecord, CancellationToken cancellationToken)
    {
        try
        {
            await UnitOfWork.BeginTransactionAsync(cancellationToken);

            // Check if we have the Urbetrack ID
            if (string.IsNullOrEmpty(syncRecord.UrbetrackInternalId))
            {
                // As per RF04 requirements: "Ir a consultar el ID de Urbetrack en el job de eliminacion o modificacion si no existe"
                // For now, we'll use the weighing scale ID as fallback
                // In a real implementation, you would query Urbetrack to get the ID
                syncRecord.UrbetrackInternalId = syncRecord.WeighingScaleId.ToString();
                Logger.LogWarning("UrbetrackInternalId was null for sync record {SyncRecordId}, using WeighingScaleId as fallback", 
                    syncRecord.Id);
            }

            // Send delete request to Urbetrack
            await _legacyApiService.DeleteUnloadingTicketAsync(syncRecord.UrbetrackInternalId);

            // Update synchronization record as cancelled
            await UpdateSynchronizationRecordAsync(syncRecord, SynchronizationStatus.Cancelled, 
                cancellationToken: cancellationToken);

            await UnitOfWork.CommitAsync(cancellationToken);

            Logger.LogInformation("Successfully synchronized deleted unloading ticket for WeighingScale {WeighingScaleId} with UrbetrackId {UrbetrackId}", 
                syncRecord.WeighingScaleId, syncRecord.UrbetrackInternalId);
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "Error synchronizing deleted unloading ticket for WeighingScale {WeighingScaleId}: {ErrorMessage}", 
                syncRecord.WeighingScaleId, ex.Message);

            try
            {
                await UpdateSynchronizationRecordAsync(syncRecord, SynchronizationStatus.Error, 
                    incrementRetryCount: true, cancellationToken: cancellationToken);
                
                await UnitOfWork.CommitAsync(cancellationToken);
            }
            catch (Exception updateEx)
            {
                Logger.LogError(updateEx, "Error updating synchronization record {SyncRecordId} after failure: {ErrorMessage}", 
                    syncRecord.Id, updateEx.Message);
                
                await UnitOfWork.RollbackAsync(cancellationToken);
            }
        }
    }
}
