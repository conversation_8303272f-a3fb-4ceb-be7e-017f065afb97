using Microsoft.Extensions.Logging;
using Reports.Application.Services.Http.Legacy;
using Reports.Domain;
using Reports.Domain.Constants;

namespace Reports.Application.Services.Synchronization.Executors;

/// <summary>
/// Job executor responsible for synchronizing deleted unloading tickets to Urbetrack.
/// Processes WeighingScale records with PendingCancellation status.
/// </summary>
public class SyncUnloadingTicketDeletionExecutor : BaseJobExecutor
{
    private readonly ILegacyApiService _legacyApiService;
    private const int MaxRetryCount = 3;

    public SyncUnloadingTicketDeletionExecutor(
        IReportsUnitOfWork unitOfWork,
        ILogger<SyncUnloadingTicketDeletionExecutor> logger,
        ILegacyApiService legacyApiService)
        : base(unitOfWork, logger)
    {
        _legacyApiService = legacyApiService;
    }

    protected override async Task ExecuteJobAsync(CancellationToken cancellationToken)
    {
        var pendingRecords = await GetSynchronizationRecordsAsync(
            SynchronizationStatus.PendingCancellation, 
            MaxRetryCount, 
            cancellationToken);

        if (!pendingRecords.Any())
        {
            Logger.LogInformation("No pending cancellation records found for synchronization");
            return;
        }

        Logger.LogInformation("Found {Count} pending cancellation records for synchronization", pendingRecords.Count);

        foreach (var syncRecord in pendingRecords)
        {
            await ProcessSyncRecordAsync(syncRecord, cancellationToken);
        }
    }

    private async Task ProcessSyncRecordAsync(Domain.Entities.UrbetrackSynchronization syncRecord, CancellationToken cancellationToken)
    {
        try
        {
            await UnitOfWork.BeginTransactionAsync(cancellationToken);

            // Check if we have the Urbetrack ID
            if (string.IsNullOrEmpty(syncRecord.UrbetrackInternalId))
            {
                Logger.LogInformation("UrbetrackInternalId not found for sync record {SyncRecordId}, searching by WeighingId {WeighingId}",
                    syncRecord.Id, syncRecord.WeighingScaleId);

                var searchResults = await _legacyApiService.SearchUnloadingTicketsByWeighingIdAsync(syncRecord.WeighingScaleId);

                if (searchResults.Length == 0)
                {
                    Logger.LogWarning("No unloading tickets found in Urbetrack for WeighingId {WeighingId}, marking as cancelled",
                        syncRecord.WeighingScaleId);

                    // If no ticket exists in Urbetrack, consider the deletion as successful
                    await UpdateSynchronizationRecordAsync(syncRecord, SynchronizationStatus.Cancelled,
                        cancellationToken: cancellationToken);
                    await UnitOfWork.CommitAsync(cancellationToken);
                    return;
                }

                // Use the first result (there should typically be only one)
                syncRecord.UrbetrackInternalId = searchResults[0].UrbetrackInternalId;
                Logger.LogInformation("Found UrbetrackInternalId {UrbetrackId} for WeighingId {WeighingId}",
                    syncRecord.UrbetrackInternalId, syncRecord.WeighingScaleId);
            }

            // Send delete request to Urbetrack
            await _legacyApiService.DeleteUnloadingTicketAsync(syncRecord.UrbetrackInternalId);

            // Update synchronization record as cancelled
            await UpdateSynchronizationRecordAsync(syncRecord, SynchronizationStatus.Cancelled,
                cancellationToken: cancellationToken);

            await UnitOfWork.CommitAsync(cancellationToken);

            Logger.LogInformation("Successfully synchronized deleted unloading ticket for WeighingScale {WeighingScaleId} with UrbetrackId {UrbetrackId}",
                syncRecord.WeighingScaleId, syncRecord.UrbetrackInternalId);
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "Error synchronizing deleted unloading ticket for WeighingScale {WeighingScaleId}: {ErrorMessage}",
                syncRecord.WeighingScaleId, ex.Message);

            try
            {
                await UpdateSynchronizationRecordAsync(syncRecord, SynchronizationStatus.Error,
                    incrementRetryCount: true, cancellationToken: cancellationToken);

                await UnitOfWork.CommitAsync(cancellationToken);
            }
            catch (Exception updateEx)
            {
                Logger.LogError(updateEx, "Error updating synchronization record {SyncRecordId} after failure: {ErrorMessage}",
                    syncRecord.Id, updateEx.Message);

                await UnitOfWork.RollbackAsync(cancellationToken);
            }
        }
    }
}
