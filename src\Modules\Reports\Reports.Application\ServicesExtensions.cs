using System.Reflection;
using FluentValidation;
using MediatR;
using Microsoft.Extensions.DependencyInjection;
using Reports.Application.Services.Synchronization;
using Reports.Application.Services.Synchronization.Executors;

namespace Reports.Application;

public static class ServicesExtensions
{
    public static IServiceCollection AddReportsApplication(this IServiceCollection services)
    {
        services.AddMediatR(Assembly.GetExecutingAssembly());

        services.AddAutoMapper(Assembly.GetExecutingAssembly());

        services.AddValidatorsFromAssembly(Assembly.GetExecutingAssembly());

        services.AddSynchronizationServices();

        return services;
    }

    private static IServiceCollection AddSynchronizationServices(this IServiceCollection services)
    {
        // Register the job factory
        services.AddScoped<IJobFactory, JobFactory>();

        // Register job executors
        services.AddScoped<SyncNewUnloadingTicketExecutor>();
        services.AddScoped<SyncUnloadingTicketModificationExecutor>();
        services.AddScoped<SyncUnloadingTicketDeletionExecutor>();

        // Register the main scheduler as a hosted service
        services.AddHostedService<MainSchedulerJob>();

        return services;
    }
}