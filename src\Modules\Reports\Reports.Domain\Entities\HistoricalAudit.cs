using System.Text.Json;
using Orion.SharedKernel.Domain.Entities;
using Reports.Domain.Constants;
using Reports.Domain.Configurations;

namespace Reports.Domain.Entities;

public class HistoricalAudit : Entity<int>
{
    public string EntityId { get; set; } = string.Empty;
    public string TableName { get; set; } = string.Empty;
    public HistoricalMovementType ActionType { get; set; }
    public string User { get; set; } = string.Empty;
    public DateTime ActionDate { get; set; }
    public string? PreviousData { get; set; }

    public HistoricalAudit() { }

    public static Builder ForWeighingScales(IEnumerable<WeighingScale> weighingScales)
    {
        var auditRecords = new List<HistoricalAudit>();

        foreach (var weighingScale in weighingScales)
        {
            var audit = new HistoricalAudit
            {
                EntityId = weighingScale.Id.ToString(),
                TableName = ReportsTableNames.WeighingScale,
                ActionDate = DateTime.SpecifyKind(DateTime.UtcNow, DateTimeKind.Unspecified)
            };
            auditRecords.Add(audit);
        }

        return new Builder(auditRecords);
    }

    public class Builder
    {
        private readonly List<HistoricalAudit> _auditRecords;

        internal Builder(List<HistoricalAudit> auditRecords)
        {
            _auditRecords = auditRecords;
        }

        public Builder WithAction(HistoricalMovementType actionType)
        {
            foreach (var audit in _auditRecords)
            {
                audit.ActionType = actionType;
            }
            return this;
        }

        public Builder ByUser(string user)
        {
            foreach (var audit in _auditRecords)
            {
                audit.User = user;
            }
            return this;
        }

        public Builder WithPreviousData<TEntity>(IEnumerable<TEntity>? previousEntities = null) where TEntity : class
        {
            if (previousEntities == null) return this;

            var previousList = previousEntities.ToList();

            for (int i = 0; i < _auditRecords.Count && i < previousList.Count; i++)
            {
                var previousEntity = previousList[i];
                _auditRecords[i].PreviousData = JsonSerializer.Serialize(previousEntity, SerializationConfiguration.DefaultJsonOptions);
            }

            return this;
        }

        public List<HistoricalAudit> Build()
        {
            return _auditRecords;
        }
    }
}
