using Reports.Domain.Constants;

namespace Reports.Application.Services.Synchronization;

/// <summary>
/// Interface for the job factory that creates job executor instances.
/// </summary>
public interface IJobFactory
{
    /// <summary>
    /// Creates a job executor instance based on the job type.
    /// </summary>
    /// <param name="jobType">The type of job executor to create</param>
    /// <returns>An instance of the job executor, or null if the job type is not supported</returns>
    IJobExecutor? CreateJob(JobExecutor jobType);
}
