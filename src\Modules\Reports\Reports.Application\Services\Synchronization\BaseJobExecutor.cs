using Microsoft.Extensions.Logging;
using Reports.Domain;
using Reports.Domain.Constants;
using Reports.Domain.Entities;

namespace Reports.Application.Services.Synchronization;

/// <summary>
/// Base class for all synchronization job executors providing common functionality.
/// </summary>
public abstract class BaseJobExecutor : IJobExecutor
{
    protected readonly IReportsUnitOfWork UnitOfWork;
    protected readonly ILogger Logger;

    protected BaseJobExecutor(IReportsUnitOfWork unitOfWork, ILogger logger)
    {
        UnitOfWork = unitOfWork;
        Logger = logger;
    }

    public async Task ExecuteAsync(CancellationToken cancellationToken = default)
    {
        try
        {
            Logger.LogInformation("Starting execution of {JobType}", GetType().Name);
            
            await ExecuteJobAsync(cancellationToken);
            
            Logger.LogInformation("Completed execution of {JobType}", GetType().Name);
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "Error executing {JobType}: {ErrorMessage}", GetType().Name, ex.Message);
            throw;
        }
    }

    /// <summary>
    /// Executes the specific job logic. Must be implemented by derived classes.
    /// </summary>
    /// <param name="cancellationToken">Cancellation token for the operation</param>
    /// <returns>A task representing the asynchronous operation</returns>
    protected abstract Task ExecuteJobAsync(CancellationToken cancellationToken);

    /// <summary>
    /// Updates the synchronization record status and retry count.
    /// </summary>
    /// <param name="syncRecord">The synchronization record to update</param>
    /// <param name="status">The new status</param>
    /// <param name="urbetrackId">The Urbetrack internal ID (optional)</param>
    /// <param name="incrementRetryCount">Whether to increment the retry count</param>
    /// <param name="cancellationToken">Cancellation token</param>
    protected async Task UpdateSynchronizationRecordAsync(
        UrbetrackSynchronization syncRecord,
        SynchronizationStatus status,
        string? urbetrackId = null,
        bool incrementRetryCount = false,
        CancellationToken cancellationToken = default)
    {
        syncRecord.Status = status;
        syncRecord.LastSynchronization = DateTime.UtcNow;
        
        if (!string.IsNullOrEmpty(urbetrackId))
        {
            syncRecord.UrbetrackInternalId = urbetrackId;
        }
        
        if (incrementRetryCount)
        {
            syncRecord.RetryCount++;
        }
        else if (status == SynchronizationStatus.Synchronized)
        {
            // Reset retry count on successful synchronization
            syncRecord.RetryCount = 0;
        }

        await UnitOfWork.UrbetrackSynchronizations.UpdateAsync(syncRecord, cancellationToken);
    }

    /// <summary>
    /// Gets synchronization records by status with optional retry count filtering.
    /// </summary>
    /// <param name="status">The synchronization status to filter by</param>
    /// <param name="maxRetryCount">Maximum retry count (optional)</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>List of synchronization records</returns>
    protected async Task<List<UrbetrackSynchronization>> GetSynchronizationRecordsAsync(
        SynchronizationStatus status,
        int? maxRetryCount = null,
        CancellationToken cancellationToken = default)
    {
        var query = UnitOfWork.UrbetrackSynchronizations.GetQueryable()
            .Where(sync => sync.Status == status);

        if (maxRetryCount.HasValue)
        {
            query = query.Where(sync => sync.RetryCount <= maxRetryCount.Value);
        }

        return await UnitOfWork.UrbetrackSynchronizations.GetListAsync(query, cancellationToken);
    }
}
