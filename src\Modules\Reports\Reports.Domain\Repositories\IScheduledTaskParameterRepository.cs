using System.Linq.Expressions;
using Orion.SharedKernel.Domain.Repositories.Entities;
using Reports.Domain.Entities;

namespace Reports.Domain.Repositories;

public interface IScheduledTaskParameterRepository : IRepository<ScheduledTaskParameter, int>
{
    Task<List<ScheduledTaskParameter>> GetListAsync(Expression<Func<ScheduledTaskParameter, bool>> predicate, CancellationToken cancellationToken);
    Task UpdateAsync(ScheduledTaskParameter entity, CancellationToken cancellationToken);
}
