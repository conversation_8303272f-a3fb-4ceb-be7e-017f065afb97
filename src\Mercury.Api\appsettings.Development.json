{"ConnectionStrings": {"PostgreSql": "{secret}"}, "AppName": "{secret}", "Logging": {"LogLevel": {"Default": "Information", "Microsoft.AspNetCore": "Warning"}}, "BusinessCodesConfiguration": {"AppCode": "02"}, "CorsConfiguration": {"PolicyName": "MercuryPolicy", "Origins": ["*"], "Methods": ["*"], "Headers": ["*"], "ExposedHeaders": ["*"]}, "JwtConfiguration": {"Secret": "{secret}", "Audience": "{secret}", "Issuer": "{secret}"}, "LegacyApiConfiguration": {"UrlBase": "{secret}", "UnloadingTicketEndpoint": "/api/UnloadingTicket/UnloadingTicket", "UpdateUnloadingTicketEndpoint": "/api/UnloadingTicket/UpdateUnloadingTicket/{id}", "DeleteUnloadingTicketEndpoint": "/api/UnloadingTicket/DeleteUnloadingTicket/{id}"}, "OrionApiConfiguration": {"UrlBase": "{secret}", "GetDomainValuesByCode": "v1/ddv/domainValues/byDomainCode/{0}", "MaterialGroupTypeDDVCode": "{secret}", "MaterialPresentationDDVCode": "{secret}"}, "ActionsConfiguration": {"Actions": {"Security.Integration.Urbetrack.Api.Controllers.v1.AuthenticationController.Authenticate": "00", "Reports.Api.Controllers.v1.Web.CollectionsController.CreateCollections": "01", "Reports.Api.Controllers.v1.Web.RecyclingAreaController.GetAll": "02", "Reports.Api.Controllers.v1.Web.ReportsController.GetReportPreview": "03", "Reports.Api.Controllers.v1.Web.ReportsController.ExportReport": "04", "Reports.Api.Controllers.v1.Web.WeighinsController.CreateWeighing": "05"}}}