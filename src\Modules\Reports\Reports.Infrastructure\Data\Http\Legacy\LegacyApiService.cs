using System.Text.Json;
using Microsoft.Extensions.Options;
using Orion.SharedKernel.Application.Common.Services;
using Orion.SharedKernel.Infrastructure.Data.Http;
using Reports.Application.Services.Http.Legacy;
using Reports.Application.Services.Http.Legacy.Request;
using Reports.Domain.Configurations;

namespace Reports.Infrastructure.Data.Http.Legacy;

public class LegacyApiService: ApiService, ILegacyApiService
{
    private readonly LegacyApiConfiguration _legacyApiConfiguration;
    private readonly SemaphoreSlim _semaphore;

    public LegacyApiService(IOptions<LegacyApiConfiguration> legacyApiConfiguration, ICacheService cacheService, HttpClient httpClient, SemaphoreSlim semaphore) : base(cacheService, httpClient)
    {
        _legacyApiConfiguration = legacyApiConfiguration.Value;
        _semaphore = semaphore;
    }
    
    public async Task SendUnloadingTicketAsync(UnloadingTicketRequest request)
    {
        await _semaphore.WaitAsync();

        try
        {
            var endpoint = request.ToRoute(_legacyApiConfiguration.UnloadingTicketEndpoint);

            var headersParams = request.ToHeader();

            await Post(endpoint: endpoint, headersParams: headersParams, body: request);
        }
        finally
        {
            _semaphore.Release();
        }
    }

    public async Task UpdateUnloadingTicketAsync(UnloadingTicketRequest request, string urbetrackId)
    {
        await _semaphore.WaitAsync();

        try
        {
            var endpoint = request.ToRoute(_legacyApiConfiguration.UpdateUnloadingTicketEndpoint.Replace("{id}", urbetrackId));

            var headersParams = request.ToHeader();

            await Put(endpoint: endpoint, headersParams: headersParams, body: request);
        }
        finally
        {
            _semaphore.Release();
        }
    }

    public async Task DeleteUnloadingTicketAsync(string urbetrackId)
    {
        await _semaphore.WaitAsync();

        try
        {
            var endpoint = _legacyApiConfiguration.DeleteUnloadingTicketEndpoint.Replace("{id}", urbetrackId);

            await Delete(endpoint: endpoint);
        }
        finally
        {
            _semaphore.Release();
        }
    }
}