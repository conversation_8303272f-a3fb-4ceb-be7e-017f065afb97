using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using Reports.Application.Services.Synchronization;
using Reports.Application.Services.Synchronization.Executors;
using Reports.Domain.Constants;

namespace Reports.Tests.Services.Synchronization;

public class JobFactoryTests
{
    private readonly Mock<IServiceProvider> _mockServiceProvider;
    private readonly Mock<ILogger<JobFactory>> _mockLogger;
    private readonly JobFactory _jobFactory;

    public JobFactoryTests()
    {
        _mockServiceProvider = new Mock<IServiceProvider>();
        _mockLogger = new Mock<ILogger<JobFactory>>();
        _jobFactory = new JobFactory(_mockServiceProvider.Object, _mockLogger.Object);
    }

    [Fact]
    public void CreateJob_WithSyncNewUnloadingTicket_ShouldReturnCorrectExecutor()
    {
        // Arrange
        var expectedExecutor = new Mock<SyncNewUnloadingTicketExecutor>(
            Mock.Of<Reports.Domain.IReportsUnitOfWork>(),
            Mock.Of<ILogger<SyncNewUnloadingTicketExecutor>>(),
            Mock.Of<Reports.Application.Services.Http.Legacy.ILegacyApiService>(),
            Mock.Of<AutoMapper.IMapper>(),
            Mock.Of<Orion.SharedKernel.Application.Common.Services.ICurrentUserService>()).Object;

        _mockServiceProvider.Setup(x => x.GetRequiredService<SyncNewUnloadingTicketExecutor>())
            .Returns(expectedExecutor);

        // Act
        var result = _jobFactory.CreateJob(JobExecutor.SyncNewUnloadingTicket);

        // Assert
        result.Should().NotBeNull();
        result.Should().BeOfType<SyncNewUnloadingTicketExecutor>();
        _mockServiceProvider.Verify(x => x.GetRequiredService<SyncNewUnloadingTicketExecutor>(), Times.Once);
    }

    [Fact]
    public void CreateJob_WithSyncUnloadingTicketModification_ShouldReturnCorrectExecutor()
    {
        // Arrange
        var expectedExecutor = new Mock<SyncUnloadingTicketModificationExecutor>(
            Mock.Of<Reports.Domain.IReportsUnitOfWork>(),
            Mock.Of<ILogger<SyncUnloadingTicketModificationExecutor>>(),
            Mock.Of<Reports.Application.Services.Http.Legacy.ILegacyApiService>(),
            Mock.Of<AutoMapper.IMapper>(),
            Mock.Of<Orion.SharedKernel.Application.Common.Services.ICurrentUserService>()).Object;

        _mockServiceProvider.Setup(x => x.GetRequiredService<SyncUnloadingTicketModificationExecutor>())
            .Returns(expectedExecutor);

        // Act
        var result = _jobFactory.CreateJob(JobExecutor.SyncUnloadingTicketModification);

        // Assert
        result.Should().NotBeNull();
        result.Should().BeOfType<SyncUnloadingTicketModificationExecutor>();
        _mockServiceProvider.Verify(x => x.GetRequiredService<SyncUnloadingTicketModificationExecutor>(), Times.Once);
    }

    [Fact]
    public void CreateJob_WithSyncUnloadingTicketDeletion_ShouldReturnCorrectExecutor()
    {
        // Arrange
        var expectedExecutor = new Mock<SyncUnloadingTicketDeletionExecutor>(
            Mock.Of<Reports.Domain.IReportsUnitOfWork>(),
            Mock.Of<ILogger<SyncUnloadingTicketDeletionExecutor>>(),
            Mock.Of<Reports.Application.Services.Http.Legacy.ILegacyApiService>()).Object;

        _mockServiceProvider.Setup(x => x.GetRequiredService<SyncUnloadingTicketDeletionExecutor>())
            .Returns(expectedExecutor);

        // Act
        var result = _jobFactory.CreateJob(JobExecutor.SyncUnloadingTicketDeletion);

        // Assert
        result.Should().NotBeNull();
        result.Should().BeOfType<SyncUnloadingTicketDeletionExecutor>();
        _mockServiceProvider.Verify(x => x.GetRequiredService<SyncUnloadingTicketDeletionExecutor>(), Times.Once);
    }

    [Fact]
    public void CreateJob_WithInvalidJobType_ShouldReturnNull()
    {
        // Act
        var result = _jobFactory.CreateJob(JobExecutor.Invalid);

        // Assert
        result.Should().BeNull();
        _mockServiceProvider.Verify(x => x.GetRequiredService(It.IsAny<Type>()), Times.Never);
    }

    [Fact]
    public void CreateJob_WithUnknownJobType_ShouldReturnNull()
    {
        // Act
        var result = _jobFactory.CreateJob((JobExecutor)999);

        // Assert
        result.Should().BeNull();
        _mockServiceProvider.Verify(x => x.GetRequiredService(It.IsAny<Type>()), Times.Never);
    }

    [Fact]
    public void CreateJob_WhenServiceProviderThrowsException_ShouldReturnNullAndLogError()
    {
        // Arrange
        _mockServiceProvider.Setup(x => x.GetRequiredService<SyncNewUnloadingTicketExecutor>())
            .Throws(new InvalidOperationException("Service not registered"));

        // Act
        var result = _jobFactory.CreateJob(JobExecutor.SyncNewUnloadingTicket);

        // Assert
        result.Should().BeNull();
        
        // Verify that error was logged
        _mockLogger.Verify(
            x => x.Log(
                LogLevel.Error,
                It.IsAny<EventId>(),
                It.Is<It.IsAnyType>((v, t) => v.ToString()!.Contains("Error creating job executor")),
                It.IsAny<Exception>(),
                It.IsAny<Func<It.IsAnyType, Exception?, string>>()),
            Times.Once);
    }
}
