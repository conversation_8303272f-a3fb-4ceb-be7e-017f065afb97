using System.Linq.Expressions;
using Orion.SharedKernel.Domain.Repositories.Entities;
using Reports.Domain.Entities;

namespace Reports.Domain.Repositories;

public interface IUrbetrackSynchronizationRepository : IRepository<UrbetrackSynchronization, int>
{
    Task<UrbetrackSynchronization?> GetByConditionAsync(Expression<Func<UrbetrackSynchronization, bool>> predicate, CancellationToken cancellationToken);
    Task<List<UrbetrackSynchronization>> GetListAsync(IQueryable<UrbetrackSynchronization> query, CancellationToken cancellationToken);
    IQueryable<UrbetrackSynchronization> GetQueryable();
    Task BulkInsertAsync(List<UrbetrackSynchronization> entities, CancellationToken cancellationToken);
    Task InsertAsync(UrbetrackSynchronization entity, CancellationToken cancellationToken);
    Task UpdateAsync(UrbetrackSynchronization entity, CancellationToken cancellationToken);
}
