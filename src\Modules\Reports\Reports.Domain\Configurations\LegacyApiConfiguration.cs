using Orion.SharedKernel.Domain.Configurations;

namespace Reports.Domain.Configurations;

public class LegacyApiConfiguration : ApiConfiguration
{
    public string UrlBase { get; set; } = null!;
    public string UnloadingTicketEndpoint { get; set; } = null!;
    public string UpdateUnloadingTicketEndpoint { get; set; } = null!;
    public string DeleteUnloadingTicketEndpoint { get; set; } = null!;
}