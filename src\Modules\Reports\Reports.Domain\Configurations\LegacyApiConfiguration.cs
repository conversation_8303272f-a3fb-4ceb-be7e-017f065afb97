using Orion.SharedKernel.Domain.Configurations;

namespace Reports.Domain.Configurations;

public class LegacyApiConfiguration : ApiConfiguration
{
    public new string UrlBase { get; set; } = null!;
    public string UnloadingTicketEndpoint { get; set; } = null!;
    public string DeleteUnloadingTicketEndpoint { get; set; } = "/api/UnloadingTicket/UnloadingTicket/{id}";
    public string SearchUnloadingTicketEndpoint { get; set; } = "/api/UnloadingTicket/Search";
}