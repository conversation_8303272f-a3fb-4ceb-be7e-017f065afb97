using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using Reports.Application.Services.Synchronization.Executors;
using Reports.Domain.Constants;

namespace Reports.Application.Services.Synchronization;

/// <summary>
/// Factory class that creates job executor instances based on job type.
/// Uses dependency injection to provide the necessary dependencies to each job.
/// </summary>
public class JobFactory : IJobFactory
{
    private readonly IServiceProvider _serviceProvider;
    private readonly ILogger<JobFactory> _logger;

    public JobFactory(IServiceProvider serviceProvider, ILogger<JobFactory> logger)
    {
        _serviceProvider = serviceProvider;
        _logger = logger;
    }

    public IJobExecutor? CreateJob(JobExecutor jobType)
    {
        try
        {
            return jobType switch
            {
                JobExecutor.SyncNewUnloadingTicket => _serviceProvider.GetRequiredService<SyncNewUnloadingTicketExecutor>(),
                JobExecutor.SyncUnloadingTicketModification => _serviceProvider.GetRequiredService<SyncUnloadingTicketModificationExecutor>(),
                JobExecutor.SyncUnloadingTicketDeletion => _serviceProvider.GetRequiredService<SyncUnloadingTicketDeletionExecutor>(),
                JobExecutor.Invalid => null,
                _ => null
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error creating job executor for job type {JobType}: {ErrorMessage}", jobType, ex.Message);
            return null;
        }
    }
}
