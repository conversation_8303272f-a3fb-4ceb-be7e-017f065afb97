using Reports.Application.Features.Web.Events.Weighins.Synchronization;
using Reports.Domain;
using Reports.Domain.Constants;
using Reports.Domain.Entities;
using Reports.Domain.Events;
using Reports.Domain.Repositories;

namespace Reports.Tests.Application.Features.Events.Synchronization;

public class WeighingScaleSyncEventHandlersTests
{
    private readonly Mock<IReportsUnitOfWork> _mockUnitOfWork;
    private readonly Mock<IUrbetrackSynchronizationRepository> _mockSyncRepository;

    public WeighingScaleSyncEventHandlersTests()
    {
        _mockUnitOfWork = new Mock<IReportsUnitOfWork>();
        _mockSyncRepository = new Mock<IUrbetrackSynchronizationRepository>();

        _mockUnitOfWork.Setup(x => x.UrbetrackSynchronizations)
            .Returns(_mockSyncRepository.Object);
    }

    [Fact]
    public async Task WeighingScaleCreatedSyncEventHandler_ShouldCreateSyncRecords()
    {
        // Arrange
        var weighingScales = new List<WeighingScale>
        {
            new WeighingScale { Id = 1, LicensePlate = "ABC123", NIT = 123456789, TownCode = "05001", MaterialType = "Test" },
            new WeighingScale { Id = 2, LicensePlate = "DEF456", NIT = 987654321, TownCode = "05001", MaterialType = "Test" }
        };

        var user = "TestUser";
        var handler = new WeighingScaleCreatedSyncEventHandler(_mockUnitOfWork.Object);
        var domainEvent = new WeighingScaleCreatedEvent(weighingScales, user);

        // Act
        await handler.Handle(domainEvent, CancellationToken.None);

        // Assert
        _mockSyncRepository.Verify(x => x.BulkInsertAsync(
            It.Is<List<UrbetrackSynchronization>>(syncs =>
                syncs.Count == 2 &&
                syncs.All(s => s.Status == SynchronizationStatus.PendingCreation) &&
                syncs.All(s => s.RetryCount == 0) &&
                syncs.All(s => s.LastSynchronization == null) &&
                syncs.All(s => s.UrbetrackInternalId == null) &&
                syncs.Any(s => s.WeighingScaleId == 1) &&
                syncs.Any(s => s.WeighingScaleId == 2)),
            CancellationToken.None), Times.Once);

        _mockUnitOfWork.Verify(x => x.BeginTransactionAsync(CancellationToken.None), Times.Once);
        _mockUnitOfWork.Verify(x => x.CommitAsync(CancellationToken.None), Times.Once);
    }

    [Fact]
    public async Task WeighingScaleUpdatedSyncEventHandler_ShouldUpdateExistingSyncRecord()
    {
        // Arrange
        var weighingScales = new List<WeighingScale>
        {
            new WeighingScale { Id = 1, LicensePlate = "ABC123", NIT = 123456789, TownCode = "05001", MaterialType = "Test" }
        };

        var existingSyncRecord = new UrbetrackSynchronization
        {
            Id = 1,
            WeighingScaleId = 1,
            Status = SynchronizationStatus.Synchronized,
            RetryCount = 0,
            UrbetrackInternalId = "URB123"
        };

        var user = "TestUser";
        var handler = new WeighingScaleUpdatedSyncEventHandler(_mockUnitOfWork.Object);
        var domainEvent = new WeighingScaleUpdatedEvent(weighingScales, weighingScales, user);

        _mockSyncRepository.Setup(x => x.GetByConditionAsync(
            It.IsAny<System.Linq.Expressions.Expression<Func<UrbetrackSynchronization, bool>>>(),
            CancellationToken.None))
            .ReturnsAsync(existingSyncRecord);

        // Act
        await handler.Handle(domainEvent, CancellationToken.None);

        // Assert
        _mockSyncRepository.Verify(x => x.UpdateAsync(
            It.Is<UrbetrackSynchronization>(sync =>
                sync.Status == SynchronizationStatus.PendingModification &&
                sync.RetryCount == 0),
            CancellationToken.None), Times.Once);

        _mockUnitOfWork.Verify(x => x.BeginTransactionAsync(CancellationToken.None), Times.Once);
        _mockUnitOfWork.Verify(x => x.CommitAsync(CancellationToken.None), Times.Once);
    }

    [Fact]
    public async Task WeighingScaleUpdatedSyncEventHandler_ShouldCreateNewSyncRecordIfNotExists()
    {
        // Arrange
        var weighingScales = new List<WeighingScale>
        {
            new WeighingScale { Id = 1, LicensePlate = "ABC123", NIT = 123456789, TownCode = "05001", MaterialType = "Test" }
        };

        var user = "TestUser";
        var handler = new WeighingScaleUpdatedSyncEventHandler(_mockUnitOfWork.Object);
        var domainEvent = new WeighingScaleUpdatedEvent(weighingScales, weighingScales, user);

        _mockSyncRepository.Setup(x => x.GetByConditionAsync(
            It.IsAny<System.Linq.Expressions.Expression<Func<UrbetrackSynchronization, bool>>>(),
            CancellationToken.None))
            .ReturnsAsync((UrbetrackSynchronization?)null);

        // Act
        await handler.Handle(domainEvent, CancellationToken.None);

        // Assert
        _mockSyncRepository.Verify(x => x.InsertAsync(
            It.Is<UrbetrackSynchronization>(sync =>
                sync.WeighingScaleId == 1 &&
                sync.Status == SynchronizationStatus.PendingModification &&
                sync.RetryCount == 0 &&
                sync.LastSynchronization == null &&
                sync.UrbetrackInternalId == null),
            CancellationToken.None), Times.Once);

        _mockUnitOfWork.Verify(x => x.BeginTransactionAsync(CancellationToken.None), Times.Once);
        _mockUnitOfWork.Verify(x => x.CommitAsync(CancellationToken.None), Times.Once);
    }

    [Fact]
    public async Task WeighingScaleCancelledSyncEventHandler_ShouldUpdateExistingSyncRecord()
    {
        // Arrange
        var weighingScales = new List<WeighingScale>
        {
            new WeighingScale { Id = 1, LicensePlate = "ABC123", NIT = 123456789, TownCode = "05001", MaterialType = "Test" }
        };

        var existingSyncRecord = new UrbetrackSynchronization
        {
            Id = 1,
            WeighingScaleId = 1,
            Status = SynchronizationStatus.Synchronized,
            RetryCount = 0,
            UrbetrackInternalId = "URB123"
        };

        var user = "TestUser";
        var handler = new WeighingScaleCancelledSyncEventHandler(_mockUnitOfWork.Object);
        var domainEvent = new WeighingScaleCancelledEvent(weighingScales, user);

        _mockSyncRepository.Setup(x => x.GetByConditionAsync(
            It.IsAny<System.Linq.Expressions.Expression<Func<UrbetrackSynchronization, bool>>>(),
            CancellationToken.None))
            .ReturnsAsync(existingSyncRecord);

        // Act
        await handler.Handle(domainEvent, CancellationToken.None);

        // Assert
        _mockSyncRepository.Verify(x => x.UpdateAsync(
            It.Is<UrbetrackSynchronization>(sync =>
                sync.Status == SynchronizationStatus.PendingCancellation &&
                sync.RetryCount == 0),
            CancellationToken.None), Times.Once);

        _mockUnitOfWork.Verify(x => x.BeginTransactionAsync(CancellationToken.None), Times.Once);
        _mockUnitOfWork.Verify(x => x.CommitAsync(CancellationToken.None), Times.Once);
    }
}
