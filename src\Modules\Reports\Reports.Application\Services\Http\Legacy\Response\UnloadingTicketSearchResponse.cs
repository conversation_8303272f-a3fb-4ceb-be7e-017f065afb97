namespace Reports.Application.Services.Http.Legacy.Response;

/// <summary>
/// Response DTO for the Urbetrack Legacy API search endpoint.
/// Represents an unloading ticket returned by the search operation.
/// </summary>
public class UnloadingTicketSearchResponse
{
    /// <summary>
    /// The internal ID used by Urbetrack to identify this unloading ticket.
    /// This is the ID that should be used for delete operations.
    /// </summary>
    public string UrbetrackInternalId { get; set; } = null!;

    /// <summary>
    /// The WeighingId that was used to create this unloading ticket.
    /// This corresponds to the WeighingScale.Id in our system.
    /// </summary>
    public long WeighingId { get; set; }

    /// <summary>
    /// The ticket number as stored in Urbetrack.
    /// </summary>
    public string? Number { get; set; }

    /// <summary>
    /// The license plate of the vehicle.
    /// </summary>
    public string? LicensePlate { get; set; }

    /// <summary>
    /// Additional properties that might be returned by the API
    /// but are not essential for our synchronization logic.
    /// </summary>
    public string? Status { get; set; }
    public DateTime? CreatedDate { get; set; }
    public DateTime? ModifiedDate { get; set; }
}
