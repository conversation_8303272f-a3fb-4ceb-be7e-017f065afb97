using Reports.Application.Features.Web.Events.Weighins.Audit;
using Reports.Domain;
using Reports.Domain.Constants;
using Reports.Domain.Entities;
using Reports.Domain.Events;
using Reports.Domain.Repositories;

namespace Reports.Tests.Application.Features.Events.Audit;

public class WeighingScaleAuditEventHandlersTests
{
    private readonly Mock<IReportsUnitOfWork> _mockUnitOfWork;
    private readonly Mock<IHistoricalAuditRepository> _mockHistoricalAuditRepository;

    public WeighingScaleAuditEventHandlersTests()
    {
        _mockUnitOfWork = new Mock<IReportsUnitOfWork>();
        _mockHistoricalAuditRepository = new Mock<IHistoricalAuditRepository>();

        _mockUnitOfWork.Setup(x => x.HistoricalAudits)
            .Returns(_mockHistoricalAuditRepository.Object);
    }

    [Fact]
    public async Task WeighingScaleCreatedEventHandler_ShouldCreateAuditRecords()
    {
        var weighingScales = new List<WeighingScale>
        {
            new WeighingScale { Id = 1, LicensePlate = "ABC123", NIT = 123456789, TownCode = "05001", MaterialType = "Test" }
        };

        var user = "TestUser";

        var handler = new WeighingScaleCreatedEventHandler(_mockUnitOfWork.Object);
        var domainEvent = new WeighingScaleCreatedEvent(weighingScales, user);

        await handler.Handle(domainEvent, CancellationToken.None);

        _mockHistoricalAuditRepository.Verify(x => x.BulkInsertAsync(
            It.Is<List<HistoricalAudit>>(audits =>
                audits.Count == 1 &&
                audits[0].ActionType == HistoricalMovementType.Created &&
                audits[0].User == user &&
                audits[0].EntityId == "1" &&
                audits[0].TableName == ReportsTableNames.WeighingScale),
            CancellationToken.None), Times.Once);
    }

    [Fact]
    public async Task WeighingScaleUpdatedEventHandler_ShouldCreateAuditRecordsWithPreviousData()
    {
        var currentWeighingScales = new List<WeighingScale>
        {
            new WeighingScale { Id = 1, LicensePlate = "ABC123", NIT = 123456789, TownCode = "05001", MaterialType = "Test" }
        };

        var previousWeighingScales = new List<WeighingScale>
        {
            new WeighingScale { Id = 1, LicensePlate = "OLD123", NIT = 111111111, TownCode = "05001", MaterialType = "Test" }
        };

        var user = "TestUser";

        var handler = new WeighingScaleUpdatedEventHandler(_mockUnitOfWork.Object);
        var domainEvent = new WeighingScaleUpdatedEvent(currentWeighingScales, previousWeighingScales, user);

        await handler.Handle(domainEvent, CancellationToken.None);

        _mockHistoricalAuditRepository.Verify(x => x.BulkInsertAsync(
            It.Is<List<HistoricalAudit>>(audits =>
                audits.Count == 1 &&
                audits[0].ActionType == HistoricalMovementType.Modified &&
                audits[0].User == user &&
                audits[0].EntityId == "1" &&
                audits[0].TableName == ReportsTableNames.WeighingScale &&
                audits[0].PreviousData != null),
            CancellationToken.None), Times.Once);
    }

    [Fact]
    public async Task WeighingScaleCancelledEventHandler_ShouldCreateAuditRecords()
    {
        var weighingScales = new List<WeighingScale>
        {
            new WeighingScale { Id = 1, LicensePlate = "ABC123", NIT = 123456789, TownCode = "05001", MaterialType = "Test", CancelDate = DateTime.UtcNow }
        };
        
        var user = "TestUser";

        var handler = new WeighingScaleCancelledEventHandler(_mockUnitOfWork.Object);
        var domainEvent = new WeighingScaleCancelledEvent(weighingScales, user);

        await handler.Handle(domainEvent, CancellationToken.None);

        _mockHistoricalAuditRepository.Verify(x => x.BulkInsertAsync(
            It.Is<List<HistoricalAudit>>(audits =>
                audits.Count == 1 &&
                audits[0].ActionType == HistoricalMovementType.Annulled &&
                audits[0].User == user &&
                audits[0].EntityId == "1" &&
                audits[0].TableName == ReportsTableNames.WeighingScale),
            CancellationToken.None), Times.Once);
    }
}
