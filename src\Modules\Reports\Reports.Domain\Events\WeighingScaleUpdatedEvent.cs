using Orion.SharedKernel.Domain.Entities.Events;
using Reports.Domain.Entities;

namespace Reports.Domain.Events;

public class WeighingScaleUpdatedEvent : DomainEvent
{
    public WeighingScaleUpdatedEvent(IEnumerable<WeighingScale> currentWeighingScales, IEnumerable<WeighingScale> previousWeighingScales, string user)
    {
        CurrentWeighingScales = currentWeighingScales;
        PreviousWeighingScales = previousWeighingScales;
        User = user;
    }
    
    public IEnumerable<WeighingScale> CurrentWeighingScales { get; }
    public IEnumerable<WeighingScale> PreviousWeighingScales { get; }
    public string User { get; }
}
