using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using Reports.Domain;

namespace Reports.Application.Services.Synchronization;

/// <summary>
/// Main scheduler background service that runs periodically to execute scheduled synchronization tasks.
/// This is the dispatcher that queries the ParametrizacionWebjobs table and executes the appropriate jobs.
/// </summary>
public class MainSchedulerJob : BackgroundService
{
    private readonly IServiceProvider _serviceProvider;
    private readonly ILogger<MainSchedulerJob> _logger;
    private readonly TimeSpan _executionInterval = TimeSpan.FromMinutes(5); // Execute every 5 minutes

    public MainSchedulerJob(IServiceProvider serviceProvider, ILogger<MainSchedulerJob> logger)
    {
        _serviceProvider = serviceProvider;
        _logger = logger;
    }

    protected override async Task ExecuteAsync(CancellationToken stoppingToken)
    {
        _logger.LogInformation("MainSchedulerJob started");

        while (!stoppingToken.IsCancellationRequested)
        {
            try
            {
                await ExecuteScheduledJobsAsync(stoppingToken);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error in MainSchedulerJob execution: {ErrorMessage}", ex.Message);
            }

            // Wait for the next execution interval
            await Task.Delay(_executionInterval, stoppingToken);
        }

        _logger.LogInformation("MainSchedulerJob stopped");
    }

    private async Task ExecuteScheduledJobsAsync(CancellationToken cancellationToken)
    {
        using var scope = _serviceProvider.CreateScope();
        var unitOfWork = scope.ServiceProvider.GetRequiredService<IReportsUnitOfWork>();
        var jobFactory = scope.ServiceProvider.GetRequiredService<IJobFactory>();

        try
        {
            _logger.LogDebug("Checking for scheduled jobs to execute");

            // Query all enabled scheduled task parameters
            var currentTime = DateTime.UtcNow;
            var scheduledTasks = await unitOfWork.ScheduledTaskParameters
                .GetListAsync(task => task.IsEnabled && 
                             task.NextRunTime.HasValue && 
                             task.NextRunTime.Value <= currentTime, 
                             cancellationToken);

            if (!scheduledTasks.Any())
            {
                _logger.LogDebug("No scheduled tasks ready for execution");
                return;
            }

            _logger.LogInformation("Found {Count} scheduled tasks ready for execution", scheduledTasks.Count);

            foreach (var scheduledTask in scheduledTasks)
            {
                await ExecuteScheduledTaskAsync(scheduledTask, jobFactory, unitOfWork, cancellationToken);
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error querying or executing scheduled tasks: {ErrorMessage}", ex.Message);
        }
    }

    private async Task ExecuteScheduledTaskAsync(
        Domain.Entities.ScheduledTaskParameter scheduledTask,
        IJobFactory jobFactory,
        IReportsUnitOfWork unitOfWork,
        CancellationToken cancellationToken)
    {
        try
        {
            _logger.LogInformation("Executing scheduled task: {TaskName} ({JobExecutor})", 
                scheduledTask.Name, scheduledTask.JobExecutor);

            // Create the job executor instance
            var jobExecutor = jobFactory.CreateJob(scheduledTask.JobExecutor);
            
            if (jobExecutor == null)
            {
                _logger.LogWarning("Failed to create job executor for task {TaskName} with JobExecutor {JobExecutor}", 
                    scheduledTask.Name, scheduledTask.JobExecutor);
                return;
            }

            // Execute the job
            await jobExecutor.ExecuteAsync(cancellationToken);

            // Update the scheduled task's execution times
            await UpdateScheduledTaskExecutionTimesAsync(scheduledTask, unitOfWork, cancellationToken);

            _logger.LogInformation("Successfully executed scheduled task: {TaskName}", scheduledTask.Name);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error executing scheduled task {TaskName}: {ErrorMessage}", 
                scheduledTask.Name, ex.Message);
            
            // Still update the execution times even if the job failed to prevent it from running continuously
            try
            {
                await UpdateScheduledTaskExecutionTimesAsync(scheduledTask, unitOfWork, cancellationToken);
            }
            catch (Exception updateEx)
            {
                _logger.LogError(updateEx, "Error updating execution times for failed task {TaskName}: {ErrorMessage}", 
                    scheduledTask.Name, updateEx.Message);
            }
        }
    }

    private async Task UpdateScheduledTaskExecutionTimesAsync(
        Domain.Entities.ScheduledTaskParameter scheduledTask,
        IReportsUnitOfWork unitOfWork,
        CancellationToken cancellationToken)
    {
        try
        {
            await unitOfWork.BeginTransactionAsync(cancellationToken);

            scheduledTask.LastRunTime = DateTime.UtcNow;
            scheduledTask.NextRunTime = DateTime.UtcNow.AddMinutes(scheduledTask.FrequencyInMinutes);

            await unitOfWork.ScheduledTaskParameters.UpdateAsync(scheduledTask, cancellationToken);
            
            await unitOfWork.CommitAsync(cancellationToken);

            _logger.LogDebug("Updated execution times for task {TaskName}. Next run: {NextRunTime}", 
                scheduledTask.Name, scheduledTask.NextRunTime);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error updating scheduled task execution times for {TaskName}: {ErrorMessage}", 
                scheduledTask.Name, ex.Message);
            
            await unitOfWork.RollbackAsync(cancellationToken);
            throw;
        }
    }
}
