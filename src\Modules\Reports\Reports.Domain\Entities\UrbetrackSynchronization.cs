using Orion.SharedKernel.Domain.Entities;
using Reports.Domain.Constants;

namespace Reports.Domain.Entities;

public class UrbetrackSynchronization : Entity<int>
{
    public long WeighingScaleId { get; set; }
    public WeighingScale WeighingScale { get; set; }
    public SynchronizationStatus Status { get; set; }
    public int RetryCount { get; set; }
    public DateTime? LastSynchronization { get; set; }
    public string? UrbetrackInternalId { get; set; }
}
