using MediatR;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Configuration;
using Orion.SharedKernel.Infrastructure.Data.EFCore;
using Orion.SharedKernel.Infrastructure.Data.EFCore.Audit;
using Reports.Domain.Entities;
using Reports.Infrastructure.Data.EFCore.Configurations.PostgreSQL;

namespace Reports.Infrastructure.Data.EFCore;

public class ReportsDbContext : OrionDbContext
{
    public virtual DbSet<WeighingScale> WeighingScales { get; set; }
    public virtual DbSet<Town> Towns { get; set; }
    public virtual DbSet<RecyclingArea> RecyclingAreas { get; set; }
    public virtual DbSet<CollectionByMicroroute> CollectionsByMicroroute { get; set; }
    public virtual DbSet<VehicleRetrieval> VehicleRetrievals { get; set; }
    public virtual DbSet<Client> Clients { get; set; }
    public virtual DbSet<MicroRoute> MicroRoutes { get; set; }
    public virtual DbSet<Toll> Tolls { get; set; }
    public virtual DbSet<Rejection> Rejections { get; set; }
    public virtual DbSet<GeneratedServiceTicket> GeneratedServiceTickets { get; set; }
    public virtual DbSet<HistoricalAudit> HistoricalAudits { get; set; }
    public virtual DbSet<UrbetrackSynchronization> UrbetrackSynchronizations { get; set; }
    public virtual DbSet<ScheduledTaskParameter> ScheduledTaskParameters { get; set; }
    public virtual DbSet<ReportFormat14> ReportsFormat14 { get; set; }
    public virtual DbSet<ReportFormat14AditionCLU> ReportFormat14AditionCLU { get; set; }
    public virtual DbSet<ReportsFormat14AditionRecyclables> ReportFormat14AditionRecyclables { get; set; }
    public virtual DbSet<ReportFormat34> ReportsFormat34 { get; set; }
    public virtual DbSet<Distribution> Distributions { get; set; }

    public ReportsDbContext(DbContextOptions<ReportsDbContext> contextOptions,
        IConfiguration configuration,
        IMediator mediator,
        AuditableEntitySaveChangesInterceptor auditableEntitySaveChangesInterceptor)
        : base(contextOptions,
            configuration,
            mediator,
            auditableEntitySaveChangesInterceptor)
    { }

    protected override void OnModelCreating(ModelBuilder modelBuilder)
    {
        base.OnModelCreating(modelBuilder);

        ModelConfiguration(modelBuilder);
    }

    protected override void OnConfiguring(DbContextOptionsBuilder optionsBuilder)
    {
        const int commandTimeoutInSeconds = 500;

        optionsBuilder.UseNpgsql(options =>
        {
            options.CommandTimeout(commandTimeoutInSeconds);
        });

        base.OnConfiguring(optionsBuilder);
    }

    private static void ModelConfiguration(ModelBuilder modelBuilder)
    {
        modelBuilder.ApplyConfiguration(new TownConfiguration());
        modelBuilder.ApplyConfiguration(new RecyclingAreaConfiguration());
        modelBuilder.ApplyConfiguration(new WeighingScaleConfiguration());
        modelBuilder.ApplyConfiguration(new CollectionByMicrorouteConfiguration());
        modelBuilder.ApplyConfiguration(new VehicleRetrievalConfiguration());
        modelBuilder.ApplyConfiguration(new ClientConfiguration());
        modelBuilder.ApplyConfiguration(new MicroRouteConfiguration());
        modelBuilder.ApplyConfiguration(new TollConfiguration());
        modelBuilder.ApplyConfiguration(new RejectionConfiguration());
        modelBuilder.ApplyConfiguration(new GeneratedServiceTicketConfiguration());
        modelBuilder.ApplyConfiguration(new HistoricalAuditConfiguration());
        modelBuilder.ApplyConfiguration(new UrbetrackSynchronizationConfiguration());
        modelBuilder.ApplyConfiguration(new ScheduledTaskParameterConfiguration());

        modelBuilder.ApplyConfiguration(new ReportFormat14Configuration());
        modelBuilder.ApplyConfiguration(new ReportFormat14AditionCLUConfiguration());
        modelBuilder.ApplyConfiguration(new ReportFormat14AditionRecyclablesConfiguration());

        modelBuilder.ApplyConfiguration(new ReportFormat34Configuration());
        modelBuilder.ApplyConfiguration(new DistributionConfiguration());
    }
}