using AutoMapper;
using Microsoft.Extensions.Logging;
using Orion.SharedKernel.Application.Common.Services;
using Reports.Application.Services.Http.Legacy;
using Reports.Application.Services.Http.Legacy.Request;
using Reports.Domain;
using Reports.Domain.Constants;

namespace Reports.Application.Services.Synchronization.Executors;

/// <summary>
/// Job executor responsible for synchronizing modified unloading tickets to Urbetrack.
/// Processes WeighingScale records with PendingModification status.
/// </summary>
public class SyncUnloadingTicketModificationExecutor : BaseJobExecutor
{
    private readonly ILegacyApiService _legacyApiService;
    private readonly IMapper _mapper;
    private readonly ICurrentUserService _currentUserService;
    private const int MaxRetryCount = 3;

    public SyncUnloadingTicketModificationExecutor(
        IReportsUnitOfWork unitOfWork,
        ILogger<SyncUnloadingTicketModificationExecutor> logger,
        ILegacyApiService legacyApiService,
        IMapper mapper,
        ICurrentUserService currentUserService)
        : base(unitOfWork, logger)
    {
        _legacyApiService = legacyApiService;
        _mapper = mapper;
        _currentUserService = currentUserService;
    }

    protected override async Task ExecuteJobAsync(CancellationToken cancellationToken)
    {
        var pendingRecords = await GetSynchronizationRecordsAsync(
            SynchronizationStatus.PendingModification, 
            MaxRetryCount, 
            cancellationToken);

        if (!pendingRecords.Any())
        {
            Logger.LogInformation("No pending modification records found for synchronization");
            return;
        }

        Logger.LogInformation("Found {Count} pending modification records for synchronization", pendingRecords.Count);

        foreach (var syncRecord in pendingRecords)
        {
            await ProcessSyncRecordAsync(syncRecord, cancellationToken);
        }
    }

    private async Task ProcessSyncRecordAsync(Domain.Entities.UrbetrackSynchronization syncRecord, CancellationToken cancellationToken)
    {
        try
        {
            await UnitOfWork.BeginTransactionAsync(cancellationToken);

            // Load the weighing scale with its related data
            var weighingScale = await UnitOfWork.WeighingScales.GetByIdAsync(syncRecord.WeighingScaleId, cancellationToken);

            if (weighingScale == null)
            {
                Logger.LogWarning("WeighingScale with ID {WeighingScaleId} not found for sync record {SyncRecordId}",
                    syncRecord.WeighingScaleId, syncRecord.Id);

                await UpdateSynchronizationRecordAsync(syncRecord, SynchronizationStatus.Error,
                    incrementRetryCount: true, cancellationToken: cancellationToken);

                await UnitOfWork.CommitAsync(cancellationToken);
                return;
            }

            // Step 1: Query for UrbetrackInternalId if we don't have it
            if (string.IsNullOrEmpty(syncRecord.UrbetrackInternalId))
            {
                Logger.LogInformation("UrbetrackInternalId not found for sync record {SyncRecordId}, searching by WeighingId {WeighingId}",
                    syncRecord.Id, syncRecord.WeighingScaleId);

                var searchResults = await _legacyApiService.SearchUnloadingTicketsByWeighingIdAsync(syncRecord.WeighingScaleId);

                if (searchResults.Length == 0)
                {
                    Logger.LogWarning("No unloading tickets found in Urbetrack for WeighingId {WeighingId}", syncRecord.WeighingScaleId);
                    await UpdateSynchronizationRecordAsync(syncRecord, SynchronizationStatus.Error,
                        incrementRetryCount: true, cancellationToken: cancellationToken);
                    await UnitOfWork.CommitAsync(cancellationToken);
                    return;
                }

                // Use the first result (there should typically be only one)
                syncRecord.UrbetrackInternalId = searchResults[0].UrbetrackInternalId;
                Logger.LogInformation("Found UrbetrackInternalId {UrbetrackId} for WeighingId {WeighingId}",
                    syncRecord.UrbetrackInternalId, syncRecord.WeighingScaleId);
            }

            // Step 2: Delete the existing ticket
            Logger.LogInformation("Deleting existing unloading ticket with UrbetrackId {UrbetrackId}", syncRecord.UrbetrackInternalId);
            await _legacyApiService.DeleteUnloadingTicketAsync(syncRecord.UrbetrackInternalId);

            // Step 3: Create new ticket with updated data
            var request = _mapper.Map<UnloadingTicketRequest>(weighingScale);
            var authorization = _currentUserService.GetJWTLegacy();
            request.SetAuthorization(authorization);

            Logger.LogInformation("Creating new unloading ticket for WeighingScale {WeighingScaleId}", syncRecord.WeighingScaleId);
            await _legacyApiService.SendUnloadingTicketAsync(request);

            // Step 4: Retrieve the new UrbetrackInternalId
            Logger.LogInformation("Searching for newly created unloading ticket for WeighingId {WeighingId}", syncRecord.WeighingScaleId);
            var newSearchResults = await _legacyApiService.SearchUnloadingTicketsByWeighingIdAsync(syncRecord.WeighingScaleId);

            if (newSearchResults.Length == 0)
            {
                Logger.LogError("Failed to find newly created unloading ticket for WeighingId {WeighingId}", syncRecord.WeighingScaleId);
                await UpdateSynchronizationRecordAsync(syncRecord, SynchronizationStatus.Error,
                    incrementRetryCount: true, cancellationToken: cancellationToken);
                await UnitOfWork.CommitAsync(cancellationToken);
                return;
            }

            // Step 5: Update sync record with new UrbetrackInternalId
            var newUrbetrackId = newSearchResults[0].UrbetrackInternalId;
            await UpdateSynchronizationRecordAsync(syncRecord, SynchronizationStatus.Synchronized,
                newUrbetrackId, cancellationToken: cancellationToken);

            await UnitOfWork.CommitAsync(cancellationToken);

            Logger.LogInformation("Successfully synchronized modified unloading ticket for WeighingScale {WeighingScaleId}. Old UrbetrackId: {OldId}, New UrbetrackId: {NewId}",
                syncRecord.WeighingScaleId, syncRecord.UrbetrackInternalId, newUrbetrackId);
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "Error synchronizing modified unloading ticket for WeighingScale {WeighingScaleId}: {ErrorMessage}",
                syncRecord.WeighingScaleId, ex.Message);

            try
            {
                await UpdateSynchronizationRecordAsync(syncRecord, SynchronizationStatus.Error,
                    incrementRetryCount: true, cancellationToken: cancellationToken);

                await UnitOfWork.CommitAsync(cancellationToken);
            }
            catch (Exception updateEx)
            {
                Logger.LogError(updateEx, "Error updating synchronization record {SyncRecordId} after failure: {ErrorMessage}",
                    syncRecord.Id, updateEx.Message);

                await UnitOfWork.RollbackAsync(cancellationToken);
            }
        }
    }
}
