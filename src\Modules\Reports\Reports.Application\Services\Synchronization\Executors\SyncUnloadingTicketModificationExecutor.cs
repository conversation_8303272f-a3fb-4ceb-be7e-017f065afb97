using AutoMapper;
using Microsoft.Extensions.Logging;
using Orion.SharedKernel.Application.Common.Services;
using Reports.Application.Services.Http.Legacy;
using Reports.Application.Services.Http.Legacy.Request;
using Reports.Domain;
using Reports.Domain.Constants;

namespace Reports.Application.Services.Synchronization.Executors;

/// <summary>
/// Job executor responsible for synchronizing modified unloading tickets to Urbetrack.
/// Processes WeighingScale records with PendingModification status.
/// </summary>
public class SyncUnloadingTicketModificationExecutor : BaseJobExecutor
{
    private readonly ILegacyApiService _legacyApiService;
    private readonly IMapper _mapper;
    private readonly ICurrentUserService _currentUserService;
    private const int MaxRetryCount = 3;

    public SyncUnloadingTicketModificationExecutor(
        IReportsUnitOfWork unitOfWork,
        ILogger<SyncUnloadingTicketModificationExecutor> logger,
        ILegacyApiService legacyApiService,
        IMapper mapper,
        ICurrentUserService currentUserService)
        : base(unitOfWork, logger)
    {
        _legacyApiService = legacyApiService;
        _mapper = mapper;
        _currentUserService = currentUserService;
    }

    protected override async Task ExecuteJobAsync(CancellationToken cancellationToken)
    {
        var pendingRecords = await GetSynchronizationRecordsAsync(
            SynchronizationStatus.PendingModification, 
            MaxRetryCount, 
            cancellationToken);

        if (!pendingRecords.Any())
        {
            Logger.LogInformation("No pending modification records found for synchronization");
            return;
        }

        Logger.LogInformation("Found {Count} pending modification records for synchronization", pendingRecords.Count);

        foreach (var syncRecord in pendingRecords)
        {
            await ProcessSyncRecordAsync(syncRecord, cancellationToken);
        }
    }

    private async Task ProcessSyncRecordAsync(Domain.Entities.UrbetrackSynchronization syncRecord, CancellationToken cancellationToken)
    {
        try
        {
            await UnitOfWork.BeginTransactionAsync(cancellationToken);

            // Check if we have the Urbetrack ID
            if (string.IsNullOrEmpty(syncRecord.UrbetrackInternalId))
            {
                // As per RF04 requirements: "Ir a consultar el ID de Urbetrack en el job de eliminacion o modificacion si no existe"
                // For now, we'll use the weighing scale ID as fallback
                // In a real implementation, you would query Urbetrack to get the ID
                syncRecord.UrbetrackInternalId = syncRecord.WeighingScaleId.ToString();
                Logger.LogWarning("UrbetrackInternalId was null for sync record {SyncRecordId}, using WeighingScaleId as fallback", 
                    syncRecord.Id);
            }

            // Load the weighing scale with its related data
            var weighingScale = await UnitOfWork.WeighingScales.GetByIdAsync(syncRecord.WeighingScaleId, cancellationToken);
            
            if (weighingScale == null)
            {
                Logger.LogWarning("WeighingScale with ID {WeighingScaleId} not found for sync record {SyncRecordId}", 
                    syncRecord.WeighingScaleId, syncRecord.Id);
                
                await UpdateSynchronizationRecordAsync(syncRecord, SynchronizationStatus.Error, 
                    incrementRetryCount: true, cancellationToken: cancellationToken);
                
                await UnitOfWork.CommitAsync(cancellationToken);
                return;
            }

            // Map to UnloadingTicketRequest
            var request = _mapper.Map<UnloadingTicketRequest>(weighingScale);
            
            // Set authorization
            var authorization = _currentUserService.GetJWTLegacy();
            request.SetAuthorization(authorization);

            // Send update to Urbetrack
            await _legacyApiService.UpdateUnloadingTicketAsync(request, syncRecord.UrbetrackInternalId);

            // Update synchronization record as successful
            await UpdateSynchronizationRecordAsync(syncRecord, SynchronizationStatus.Synchronized, 
                cancellationToken: cancellationToken);

            await UnitOfWork.CommitAsync(cancellationToken);

            Logger.LogInformation("Successfully synchronized modified unloading ticket for WeighingScale {WeighingScaleId} with UrbetrackId {UrbetrackId}", 
                syncRecord.WeighingScaleId, syncRecord.UrbetrackInternalId);
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "Error synchronizing modified unloading ticket for WeighingScale {WeighingScaleId}: {ErrorMessage}", 
                syncRecord.WeighingScaleId, ex.Message);

            try
            {
                await UpdateSynchronizationRecordAsync(syncRecord, SynchronizationStatus.Error, 
                    incrementRetryCount: true, cancellationToken: cancellationToken);
                
                await UnitOfWork.CommitAsync(cancellationToken);
            }
            catch (Exception updateEx)
            {
                Logger.LogError(updateEx, "Error updating synchronization record {SyncRecordId} after failure: {ErrorMessage}", 
                    syncRecord.Id, updateEx.Message);
                
                await UnitOfWork.RollbackAsync(cancellationToken);
            }
        }
    }
}
