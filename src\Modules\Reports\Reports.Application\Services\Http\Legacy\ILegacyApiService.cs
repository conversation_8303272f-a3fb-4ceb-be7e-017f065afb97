using Reports.Application.Services.Http.Legacy.Request;
using Reports.Application.Services.Http.Legacy.Response;

namespace Reports.Application.Services.Http.Legacy;

public interface ILegacyApiService
{
    Task SendUnloadingTicketAsync(UnloadingTicketRequest request);
    Task DeleteUnloadingTicketAsync(string urbetrackId);
    Task<UnloadingTicketSearchResponse[]> SearchUnloadingTicketsByWeighingIdAsync(long weighingId);
}