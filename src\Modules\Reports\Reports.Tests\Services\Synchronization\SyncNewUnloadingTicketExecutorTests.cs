using AutoMapper;
using Microsoft.Extensions.Logging;
using Orion.SharedKernel.Application.Common.Services;
using Reports.Application.Services.Http.Legacy;
using Reports.Application.Services.Http.Legacy.Request;
using Reports.Application.Services.Synchronization.Executors;
using Reports.Domain;
using Reports.Domain.Constants;
using Reports.Domain.Entities;
using Reports.Domain.Repositories;
using System.Linq.Expressions;

namespace Reports.Tests.Services.Synchronization;

public class SyncNewUnloadingTicketExecutorTests
{
    private readonly Mock<IReportsUnitOfWork> _mockUnitOfWork;
    private readonly Mock<IUrbetrackSynchronizationRepository> _mockSyncRepository;
    private readonly Mock<IWeighingScaleRepository> _mockWeighingScaleRepository;
    private readonly Mock<ILogger<SyncNewUnloadingTicketExecutor>> _mockLogger;
    private readonly Mock<ILegacyApiService> _mockLegacyApiService;
    private readonly Mock<IMapper> _mockMapper;
    private readonly Mock<ICurrentUserService> _mockCurrentUserService;
    private readonly SyncNewUnloadingTicketExecutor _executor;

    public SyncNewUnloadingTicketExecutorTests()
    {
        _mockUnitOfWork = new Mock<IReportsUnitOfWork>();
        _mockSyncRepository = new Mock<IUrbetrackSynchronizationRepository>();
        _mockWeighingScaleRepository = new Mock<IWeighingScaleRepository>();
        _mockLogger = new Mock<ILogger<SyncNewUnloadingTicketExecutor>>();
        _mockLegacyApiService = new Mock<ILegacyApiService>();
        _mockMapper = new Mock<IMapper>();
        _mockCurrentUserService = new Mock<ICurrentUserService>();

        _mockUnitOfWork.Setup(x => x.UrbetrackSynchronizations).Returns(_mockSyncRepository.Object);
        _mockUnitOfWork.Setup(x => x.WeighingScales).Returns(_mockWeighingScaleRepository.Object);

        _executor = new SyncNewUnloadingTicketExecutor(
            _mockUnitOfWork.Object,
            _mockLogger.Object,
            _mockLegacyApiService.Object,
            _mockMapper.Object,
            _mockCurrentUserService.Object);
    }

    [Fact]
    public async Task ExecuteAsync_WithNoPendingRecords_ShouldLogAndReturn()
    {
        // Arrange
        var queryable = new List<UrbetrackSynchronization>().AsQueryable();
        _mockSyncRepository.Setup(x => x.GetQueryable()).Returns(queryable);
        _mockSyncRepository.Setup(x => x.GetListAsync(It.IsAny<IQueryable<UrbetrackSynchronization>>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(new List<UrbetrackSynchronization>());

        // Act
        await _executor.ExecuteAsync(CancellationToken.None);

        // Assert
        _mockLogger.Verify(
            x => x.Log(
                LogLevel.Information,
                It.IsAny<EventId>(),
                It.Is<It.IsAnyType>((v, t) => v.ToString()!.Contains("No pending creation records found")),
                It.IsAny<Exception>(),
                It.IsAny<Func<It.IsAnyType, Exception?, string>>()),
            Times.Once);

        _mockLegacyApiService.Verify(x => x.SendUnloadingTicketAsync(It.IsAny<UnloadingTicketRequest>()), Times.Never);
    }

    [Fact]
    public async Task ExecuteAsync_WithPendingRecords_ShouldProcessSuccessfully()
    {
        // Arrange
        var syncRecord = new UrbetrackSynchronization
        {
            Id = 1,
            WeighingScaleId = 100,
            Status = SynchronizationStatus.PendingCreation,
            RetryCount = 0
        };

        var weighingScale = new WeighingScale
        {
            Id = 100,
            LicensePlate = "ABC123",
            NIT = 123456789,
            TownCode = "05001",
            MaterialType = "Test"
        };

        var unloadingTicketRequest = new UnloadingTicketRequest
        {
            Number = "100",
            LicensePlate = "ABC123"
        };

        var queryable = new List<UrbetrackSynchronization> { syncRecord }.AsQueryable();
        _mockSyncRepository.Setup(x => x.GetQueryable()).Returns(queryable);
        _mockSyncRepository.Setup(x => x.GetListAsync(It.IsAny<IQueryable<UrbetrackSynchronization>>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(new List<UrbetrackSynchronization> { syncRecord });

        _mockWeighingScaleRepository.Setup(x => x.GetByIdAsync(100, It.IsAny<CancellationToken>()))
            .ReturnsAsync(weighingScale);

        _mockMapper.Setup(x => x.Map<UnloadingTicketRequest>(weighingScale))
            .Returns(unloadingTicketRequest);

        _mockCurrentUserService.Setup(x => x.GetJWTLegacy())
            .Returns("test-jwt-token");

        // Act
        await _executor.ExecuteAsync(CancellationToken.None);

        // Assert
        _mockLegacyApiService.Verify(x => x.SendUnloadingTicketAsync(It.IsAny<UnloadingTicketRequest>()), Times.Once);
        
        _mockSyncRepository.Verify(x => x.UpdateAsync(
            It.Is<UrbetrackSynchronization>(sync => 
                sync.Status == SynchronizationStatus.Synchronized &&
                sync.UrbetrackInternalId == "100"),
            It.IsAny<CancellationToken>()), Times.Once);

        _mockUnitOfWork.Verify(x => x.BeginTransactionAsync(It.IsAny<CancellationToken>()), Times.Once);
        _mockUnitOfWork.Verify(x => x.CommitAsync(It.IsAny<CancellationToken>()), Times.Once);
    }

    [Fact]
    public async Task ExecuteAsync_WithMissingWeighingScale_ShouldMarkAsError()
    {
        // Arrange
        var syncRecord = new UrbetrackSynchronization
        {
            Id = 1,
            WeighingScaleId = 100,
            Status = SynchronizationStatus.PendingCreation,
            RetryCount = 0
        };

        var queryable = new List<UrbetrackSynchronization> { syncRecord }.AsQueryable();
        _mockSyncRepository.Setup(x => x.GetQueryable()).Returns(queryable);
        _mockSyncRepository.Setup(x => x.GetListAsync(It.IsAny<IQueryable<UrbetrackSynchronization>>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(new List<UrbetrackSynchronization> { syncRecord });

        _mockWeighingScaleRepository.Setup(x => x.GetByIdAsync(100, It.IsAny<CancellationToken>()))
            .ReturnsAsync((WeighingScale?)null);

        // Act
        await _executor.ExecuteAsync(CancellationToken.None);

        // Assert
        _mockSyncRepository.Verify(x => x.UpdateAsync(
            It.Is<UrbetrackSynchronization>(sync => 
                sync.Status == SynchronizationStatus.Error &&
                sync.RetryCount == 1),
            It.IsAny<CancellationToken>()), Times.Once);

        _mockLegacyApiService.Verify(x => x.SendUnloadingTicketAsync(It.IsAny<UnloadingTicketRequest>()), Times.Never);
    }

    [Fact]
    public async Task ExecuteAsync_WithApiException_ShouldMarkAsErrorAndIncrementRetryCount()
    {
        // Arrange
        var syncRecord = new UrbetrackSynchronization
        {
            Id = 1,
            WeighingScaleId = 100,
            Status = SynchronizationStatus.PendingCreation,
            RetryCount = 1
        };

        var weighingScale = new WeighingScale
        {
            Id = 100,
            LicensePlate = "ABC123",
            NIT = 123456789,
            TownCode = "05001",
            MaterialType = "Test"
        };

        var queryable = new List<UrbetrackSynchronization> { syncRecord }.AsQueryable();
        _mockSyncRepository.Setup(x => x.GetQueryable()).Returns(queryable);
        _mockSyncRepository.Setup(x => x.GetListAsync(It.IsAny<IQueryable<UrbetrackSynchronization>>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(new List<UrbetrackSynchronization> { syncRecord });

        _mockWeighingScaleRepository.Setup(x => x.GetByIdAsync(100, It.IsAny<CancellationToken>()))
            .ReturnsAsync(weighingScale);

        _mockMapper.Setup(x => x.Map<UnloadingTicketRequest>(weighingScale))
            .Returns(new UnloadingTicketRequest());

        _mockCurrentUserService.Setup(x => x.GetJWTLegacy())
            .Returns("test-jwt-token");

        _mockLegacyApiService.Setup(x => x.SendUnloadingTicketAsync(It.IsAny<UnloadingTicketRequest>()))
            .ThrowsAsync(new HttpRequestException("API Error"));

        // Act
        await _executor.ExecuteAsync(CancellationToken.None);

        // Assert
        _mockSyncRepository.Verify(x => x.UpdateAsync(
            It.Is<UrbetrackSynchronization>(sync => 
                sync.Status == SynchronizationStatus.Error &&
                sync.RetryCount == 2),
            It.IsAny<CancellationToken>()), Times.Once);
    }
}
