using MediatR;
using Reports.Domain;
using Reports.Domain.Constants;
using Reports.Domain.Events;

namespace Reports.Application.Features.Web.Events.Weighins.Synchronization;

public class WeighingScaleCancelledSyncEventHandler : INotificationHandler<WeighingScaleCancelledEvent>
{
    private readonly IReportsUnitOfWork _unitOfWork;

    public WeighingScaleCancelledSyncEventHandler(IReportsUnitOfWork unitOfWork)
    {
        _unitOfWork = unitOfWork;
    }

    public async Task Handle(WeighingScaleCancelledEvent notification, CancellationToken cancellationToken)
    {
        await _unitOfWork.BeginTransactionAsync(cancellationToken);

        foreach (var weighingScale in notification.WeighingScales)
        {
            var existingSyncRecord = await _unitOfWork.UrbetrackSynchronizations
                .GetByConditionAsync(sync => sync.WeighingScaleId == weighingScale.Id, cancellationToken);

            if (existingSyncRecord != null)
            {
                // Update existing synchronization record to pending cancellation
                existingSyncRecord.Status = SynchronizationStatus.PendingCancellation;
                existingSyncRecord.RetryCount = 0;
                
                await _unitOfWork.UrbetrackSynchronizations.UpdateAsync(existingSyncRecord, cancellationToken);
            }
            else
            {
                // Create new synchronization record for cancellation if it doesn't exist
                // This handles cases where the weighing scale was created before the sync system was implemented
                var newSyncRecord = new Domain.Entities.UrbetrackSynchronization
                {
                    WeighingScaleId = weighingScale.Id,
                    Status = SynchronizationStatus.PendingCancellation,
                    RetryCount = 0,
                    LastSynchronization = null,
                    UrbetrackInternalId = null
                };
                
                await _unitOfWork.UrbetrackSynchronizations.InsertAsync(newSyncRecord, cancellationToken);
            }
        }
        
        await _unitOfWork.CommitAsync(cancellationToken);
    }
}
