using AutoMapper;
using Microsoft.Extensions.Logging;
using Orion.SharedKernel.Application.Common.Services;
using Reports.Application.Services.Http.Legacy;
using Reports.Application.Services.Http.Legacy.Request;
using Reports.Domain;
using Reports.Domain.Constants;

namespace Reports.Application.Services.Synchronization.Executors;

/// <summary>
/// Job executor responsible for synchronizing new unloading tickets to Urbetrack.
/// Processes WeighingScale records with PendingCreation status.
/// </summary>
public class SyncNewUnloadingTicketExecutor : BaseJobExecutor
{
    private readonly ILegacyApiService _legacyApiService;
    private readonly IMapper _mapper;
    private readonly ICurrentUserService _currentUserService;
    private const int MaxRetryCount = 3;

    public SyncNewUnloadingTicketExecutor(
        IReportsUnitOfWork unitOfWork,
        ILogger<SyncNewUnloadingTicketExecutor> logger,
        ILegacyApiService legacyApiService,
        IMapper mapper,
        ICurrentUserService currentUserService)
        : base(unitOfWork, logger)
    {
        _legacyApiService = legacyApiService;
        _mapper = mapper;
        _currentUserService = currentUserService;
    }

    protected override async Task ExecuteJobAsync(CancellationToken cancellationToken)
    {
        var pendingRecords = await GetSynchronizationRecordsAsync(
            SynchronizationStatus.PendingCreation, 
            MaxRetryCount, 
            cancellationToken);

        if (!pendingRecords.Any())
        {
            Logger.LogInformation("No pending creation records found for synchronization");
            return;
        }

        Logger.LogInformation("Found {Count} pending creation records for synchronization", pendingRecords.Count);

        foreach (var syncRecord in pendingRecords)
        {
            await ProcessSyncRecordAsync(syncRecord, cancellationToken);
        }
    }

    private async Task ProcessSyncRecordAsync(Domain.Entities.UrbetrackSynchronization syncRecord, CancellationToken cancellationToken)
    {
        try
        {
            await UnitOfWork.BeginTransactionAsync(cancellationToken);

            // Load the weighing scale with its related data
            var weighingScale = await UnitOfWork.WeighingScales.GetByIdAsync(syncRecord.WeighingScaleId, cancellationToken);
            
            if (weighingScale == null)
            {
                Logger.LogWarning("WeighingScale with ID {WeighingScaleId} not found for sync record {SyncRecordId}", 
                    syncRecord.WeighingScaleId, syncRecord.Id);
                
                await UpdateSynchronizationRecordAsync(syncRecord, SynchronizationStatus.Error, 
                    incrementRetryCount: true, cancellationToken: cancellationToken);
                
                await UnitOfWork.CommitAsync(cancellationToken);
                return;
            }

            // Map to UnloadingTicketRequest
            var request = _mapper.Map<UnloadingTicketRequest>(weighingScale);
            
            // Set authorization
            var authorization = _currentUserService.GetJWTLegacy();
            request.SetAuthorization(authorization);

            // Send to Urbetrack
            await _legacyApiService.SendUnloadingTicketAsync(request);

            // For now, we'll use the weighing scale ID as the Urbetrack ID since the API doesn't return an ID
            // In a real implementation, you would extract the ID from the API response
            var urbetrackId = weighingScale.Id.ToString();

            // Update synchronization record as successful
            await UpdateSynchronizationRecordAsync(syncRecord, SynchronizationStatus.Synchronized, 
                urbetrackId, cancellationToken: cancellationToken);

            await UnitOfWork.CommitAsync(cancellationToken);

            Logger.LogInformation("Successfully synchronized new unloading ticket for WeighingScale {WeighingScaleId}", 
                syncRecord.WeighingScaleId);
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "Error synchronizing new unloading ticket for WeighingScale {WeighingScaleId}: {ErrorMessage}", 
                syncRecord.WeighingScaleId, ex.Message);

            try
            {
                await UpdateSynchronizationRecordAsync(syncRecord, SynchronizationStatus.Error, 
                    incrementRetryCount: true, cancellationToken: cancellationToken);
                
                await UnitOfWork.CommitAsync(cancellationToken);
            }
            catch (Exception updateEx)
            {
                Logger.LogError(updateEx, "Error updating synchronization record {SyncRecordId} after failure: {ErrorMessage}", 
                    syncRecord.Id, updateEx.Message);
                
                await UnitOfWork.RollbackAsync(cancellationToken);
            }
        }
    }
}
