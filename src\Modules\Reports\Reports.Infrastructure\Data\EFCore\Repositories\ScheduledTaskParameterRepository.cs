using System.Linq.Expressions;
using Microsoft.EntityFrameworkCore;
using Orion.SharedKernel.Application.Common.Services;
using Orion.SharedKernel.Infrastructure.Data.EFCore.ContextProvider;
using Orion.SharedKernel.Infrastructure.Data.EFCore.Repositories.Entities;
using Reports.Domain.Entities;
using Reports.Domain.Repositories;

namespace Reports.Infrastructure.Data.EFCore.Repositories;

public class ScheduledTaskParameterRepository : Repository<ScheduledTaskParameter, int, ReportsDbContext>, IScheduledTaskParameterRepository
{
    public ScheduledTaskParameterRepository(IDbContextProvider<ReportsDbContext> dbContextProvider, ICacheService cacheService) : base(dbContextProvider, cacheService) { }

    public async Task<List<ScheduledTaskParameter>> GetListAsync(Expression<Func<ScheduledTaskParameter, bool>> predicate, CancellationToken cancellationToken)
    {
        return await _context.ScheduledTaskParameters
            .Where(predicate)
            .ToListAsync(cancellationToken);
    }

    public async Task UpdateAsync(ScheduledTaskParameter entity, CancellationToken cancellationToken)
    {
        _context.ScheduledTaskParameters.Update(entity);
        await _context.SaveChangesAsync(cancellationToken);
    }
}
