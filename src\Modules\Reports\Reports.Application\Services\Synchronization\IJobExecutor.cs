namespace Reports.Application.Services.Synchronization;

/// <summary>
/// Interface that defines the contract for all synchronization job executors.
/// This allows the scheduler to treat all jobs uniformly and facilitates adding new functionality.
/// </summary>
public interface IJobExecutor
{
    /// <summary>
    /// Executes the synchronization job logic.
    /// </summary>
    /// <param name="cancellationToken">Cancellation token for the operation</param>
    /// <returns>A task representing the asynchronous operation</returns>
    Task ExecuteAsync(CancellationToken cancellationToken = default);
}
