using MediatR;
using Reports.Domain;
using Reports.Domain.Constants;
using Reports.Domain.Entities;
using Reports.Domain.Events;

namespace Reports.Application.Features.Web.Events.Weighins.Synchronization;

public class WeighingScaleCreatedSyncEventHandler : INotificationHandler<WeighingScaleCreatedEvent>
{
    private readonly IReportsUnitOfWork _unitOfWork;

    public WeighingScaleCreatedSyncEventHandler(IReportsUnitOfWork unitOfWork)
    {
        _unitOfWork = unitOfWork;
    }

    public async Task Handle(WeighingScaleCreatedEvent notification, CancellationToken cancellationToken)
    {
        var synchronizationRecords = notification.WeighingScales
            .Select(weighingScale => new UrbetrackSynchronization
            {
                WeighingScaleId = weighingScale.Id,
                Status = SynchronizationStatus.PendingCreation,
                RetryCount = 0,
                LastSynchronization = null,
                UrbetrackInternalId = null
            })
            .ToList();

        await _unitOfWork.BeginTransactionAsync(cancellationToken);
        
        await _unitOfWork.UrbetrackSynchronizations.BulkInsertAsync(synchronizationRecords, cancellationToken);
        
        await _unitOfWork.CommitAsync(cancellationToken);
    }
}
