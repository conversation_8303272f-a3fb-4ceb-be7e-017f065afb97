Como un: Administrador del Sistema / Operador
Quiero: un mecanismo robusto y automatizado para sincronizar los tickets de pesaje con el sistema externo Urbetrack
Para: poder asegurar la consistencia de los datos entre ambos sistemas, gestionar fallos de comunicación de forma automática y tener una visibilidad clara del estado de cada integración.


Arquitectura General del Servicio

El servicio se basa en un patrón de Despachador + Fábrica + Ejecutor para lograr un diseño modular, desacoplado y extensible, facilitando el mantenimiento y la escalabilidad.

Despachador (Scheduler): Un trabajo principal (ScheduledJob) que se ejecuta a una frecuencia fija (por ejemplo, cada 5 minutos). Su única responsabilidad es consultar la tabla de configuración (ParametriaTareasProgramadas) y determinar qué trabajos específicos deben ejecutarse.

Fábrica (Job Factory): Una clase responsable de instanciar el objeto ejecutor correcto basándose en el JobType/Descripcion/Nombre (string) leído de la configuración. Esto evita que el despachador tenga conocimiento directo de las clases de trabajo concretas, promoviendo el desacoplamiento.

Ejecutor (Job Executor): Clases específicas, cada una implementando una interfaz común (IJobExecutor), que contienen la lógica de negocio para un tipo de tarea de sincronización (crear, modificar, anular).



Componentes Técnicos


1. El Despachador Principal (MainSchedulerJob)

Este es el punto de entrada del proceso. Es un trabajo ligero configurado para ejecutarse a intervalos regulares (por ejemplo, cada 5 minutos) en el host de .NET.

Lógica de Ejecución:

Se activa según su temporizador.

Consulta todos los registros de la tabla ParametrizacionWebjobs.

Para cada registro de configuración obtenido:

Verifica si IsEnabled == true.

Verifica si la ejecución es pertinente: NextRunTime <= DateTime.UtcNow.

Si ambas condiciones son verdaderas:

a. Invoca a JobFactory.CreateJob(job.JobType) para obtener una instancia del ejecutor.

b. Si la fábrica devuelve una instancia válida, invoca el método jobExecutor.Execute().

c. Se encapsula la ejecución en un bloque try-catch para registrar cualquier error inesperado y asegurar que el fallo de un job no detenga al despachador.

d. Al finalizar la ejecución exitosa del Execute(), actualiza el registro en ParametrizacionWebjobs:

LastRunTime = DateTime.UtcNow

NextRunTime = DateTime.UtcNow.AddMinutes(job.FrequencyInMinutes)

2. La Interfaz del Ejecutor (IJobExecutor)

Una interfaz simple que define el contrato que todas las clases de trabajo deben seguir. Esto permite al despachador tratar a todos los trabajos de manera uniforme y facilita la adición de nuevas funcionalidades.

3. La Fábrica de Trabajos (JobFactory)

Esta clase centraliza la lógica de creación de instancias de los trabajos. Decodifica el JobType (string) de la base de datos y lo mapea a una clase concreta, utilizando la Inyección de Dependencias (DI) para proporcionar las dependencias necesarias a cada job.



Implementación de los siguientes ejecutores
- Creacion de Tiquetes de Descarga
- Modificacion de Tiquetes de Descarga
- Eliminacion de Tiquetes de Descarga

Nota: Ir a consultar el ID de Urbetrack en el job de eliminacion o modificacion si no existe. En caso de que se cree y nunca se modifique no tendria el ID de Urbetrack. Con el proposito de reducir la cantidad de gets 


Criterios de Aceptacion:

1) La creación y correcta prueba y ejecución de los componentes mencionados arriba.
2) La correcta integración de los componentes mencionados.
3) Se deberán realizar las pruebas pertinentes en el ambiente local.