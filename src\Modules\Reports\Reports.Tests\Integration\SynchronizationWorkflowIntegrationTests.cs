using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using Reports.Application.Services.Synchronization;
using Reports.Domain;
using Reports.Domain.Constants;
using Reports.Domain.Entities;
using Reports.Domain.Events;

namespace Reports.Tests.Integration;

public class SynchronizationWorkflowIntegrationTests
{
    private readonly Mock<IReportsUnitOfWork> _mockUnitOfWork;
    private readonly Mock<IServiceProvider> _mockServiceProvider;
    private readonly Mock<IServiceScope> _mockServiceScope;
    private readonly Mock<IServiceScopeFactory> _mockServiceScopeFactory;
    private readonly Mock<IJobFactory> _mockJobFactory;
    private readonly Mock<IJobExecutor> _mockJobExecutor;
    private readonly Mock<ILogger<MainSchedulerJob>> _mockLogger;

    public SynchronizationWorkflowIntegrationTests()
    {
        _mockUnitOfWork = new Mock<IReportsUnitOfWork>();
        _mockServiceProvider = new Mock<IServiceProvider>();
        _mockServiceScope = new Mock<IServiceScope>();
        _mockServiceScopeFactory = new Mock<IServiceScopeFactory>();
        _mockJobFactory = new Mock<IJobFactory>();
        _mockJobExecutor = new Mock<IJobExecutor>();
        _mockLogger = new Mock<ILogger<MainSchedulerJob>>();

        // Setup service provider chain
        _mockServiceProvider.Setup(x => x.GetRequiredService<IServiceScopeFactory>())
            .Returns(_mockServiceScopeFactory.Object);
        
        _mockServiceScopeFactory.Setup(x => x.CreateScope())
            .Returns(_mockServiceScope.Object);
        
        _mockServiceScope.Setup(x => x.ServiceProvider)
            .Returns(_mockServiceProvider.Object);

        _mockServiceProvider.Setup(x => x.GetRequiredService<IReportsUnitOfWork>())
            .Returns(_mockUnitOfWork.Object);
        
        _mockServiceProvider.Setup(x => x.GetRequiredService<IJobFactory>())
            .Returns(_mockJobFactory.Object);
    }

    [Fact]
    public async Task MainSchedulerJob_WithEnabledScheduledTask_ShouldExecuteJobAndUpdateTimes()
    {
        // Arrange
        var scheduledTask = new ScheduledTaskParameter
        {
            Id = 1,
            Name = "Test Sync Job",
            JobExecutor = JobExecutor.SyncNewUnloadingTicket,
            IsEnabled = true,
            FrequencyInMinutes = 5,
            NextRunTime = DateTime.UtcNow.AddMinutes(-1), // Past time to trigger execution
            LastRunTime = null
        };

        var scheduledTasks = new List<ScheduledTaskParameter> { scheduledTask };

        _mockUnitOfWork.Setup(x => x.ScheduledTaskParameters.GetListAsync(
            It.IsAny<System.Linq.Expressions.Expression<Func<ScheduledTaskParameter, bool>>>(),
            It.IsAny<CancellationToken>()))
            .ReturnsAsync(scheduledTasks);

        _mockJobFactory.Setup(x => x.CreateJob(JobExecutor.SyncNewUnloadingTicket))
            .Returns(_mockJobExecutor.Object);

        _mockUnitOfWork.Setup(x => x.ScheduledTaskParameters.UpdateAsync(It.IsAny<ScheduledTaskParameter>(), It.IsAny<CancellationToken>()))
            .Returns(Task.CompletedTask);

        var scheduler = new MainSchedulerJob(_mockServiceProvider.Object, _mockLogger.Object);

        // Act
        using var cts = new CancellationTokenSource();
        cts.CancelAfter(TimeSpan.FromSeconds(1)); // Cancel after 1 second to stop the background service

        try
        {
            await scheduler.StartAsync(cts.Token);
            await Task.Delay(100, cts.Token); // Give it time to execute once
        }
        catch (OperationCanceledException)
        {
            // Expected when cancellation token is triggered
        }
        finally
        {
            await scheduler.StopAsync(CancellationToken.None);
        }

        // Assert
        _mockJobExecutor.Verify(x => x.ExecuteAsync(It.IsAny<CancellationToken>()), Times.AtLeastOnce);
        
        _mockUnitOfWork.Verify(x => x.ScheduledTaskParameters.UpdateAsync(
            It.Is<ScheduledTaskParameter>(task => 
                task.LastRunTime.HasValue && 
                task.NextRunTime.HasValue &&
                task.NextRunTime > task.LastRunTime),
            It.IsAny<CancellationToken>()), Times.AtLeastOnce);
    }

    [Fact]
    public async Task MainSchedulerJob_WithDisabledScheduledTask_ShouldNotExecuteJob()
    {
        // Arrange
        var scheduledTask = new ScheduledTaskParameter
        {
            Id = 1,
            Name = "Disabled Sync Job",
            JobExecutor = JobExecutor.SyncNewUnloadingTicket,
            IsEnabled = false, // Disabled
            FrequencyInMinutes = 5,
            NextRunTime = DateTime.UtcNow.AddMinutes(-1),
            LastRunTime = null
        };

        // Return empty list since disabled tasks should be filtered out
        _mockUnitOfWork.Setup(x => x.ScheduledTaskParameters.GetListAsync(
            It.IsAny<System.Linq.Expressions.Expression<Func<ScheduledTaskParameter, bool>>>(),
            It.IsAny<CancellationToken>()))
            .ReturnsAsync(new List<ScheduledTaskParameter>());

        var scheduler = new MainSchedulerJob(_mockServiceProvider.Object, _mockLogger.Object);

        // Act
        using var cts = new CancellationTokenSource();
        cts.CancelAfter(TimeSpan.FromSeconds(1));

        try
        {
            await scheduler.StartAsync(cts.Token);
            await Task.Delay(100, cts.Token);
        }
        catch (OperationCanceledException)
        {
            // Expected
        }
        finally
        {
            await scheduler.StopAsync(CancellationToken.None);
        }

        // Assert
        _mockJobExecutor.Verify(x => x.ExecuteAsync(It.IsAny<CancellationToken>()), Times.Never);
        _mockJobFactory.Verify(x => x.CreateJob(It.IsAny<JobExecutor>()), Times.Never);
    }

    [Fact]
    public async Task MainSchedulerJob_WithFutureNextRunTime_ShouldNotExecuteJob()
    {
        // Arrange
        var scheduledTask = new ScheduledTaskParameter
        {
            Id = 1,
            Name = "Future Sync Job",
            JobExecutor = JobExecutor.SyncNewUnloadingTicket,
            IsEnabled = true,
            FrequencyInMinutes = 5,
            NextRunTime = DateTime.UtcNow.AddMinutes(10), // Future time
            LastRunTime = null
        };

        // Return empty list since future tasks should be filtered out
        _mockUnitOfWork.Setup(x => x.ScheduledTaskParameters.GetListAsync(
            It.IsAny<System.Linq.Expressions.Expression<Func<ScheduledTaskParameter, bool>>>(),
            It.IsAny<CancellationToken>()))
            .ReturnsAsync(new List<ScheduledTaskParameter>());

        var scheduler = new MainSchedulerJob(_mockServiceProvider.Object, _mockLogger.Object);

        // Act
        using var cts = new CancellationTokenSource();
        cts.CancelAfter(TimeSpan.FromSeconds(1));

        try
        {
            await scheduler.StartAsync(cts.Token);
            await Task.Delay(100, cts.Token);
        }
        catch (OperationCanceledException)
        {
            // Expected
        }
        finally
        {
            await scheduler.StopAsync(CancellationToken.None);
        }

        // Assert
        _mockJobExecutor.Verify(x => x.ExecuteAsync(It.IsAny<CancellationToken>()), Times.Never);
        _mockJobFactory.Verify(x => x.CreateJob(It.IsAny<JobExecutor>()), Times.Never);
    }

    [Fact]
    public async Task MainSchedulerJob_WithJobExecutorException_ShouldContinueAndUpdateTimes()
    {
        // Arrange
        var scheduledTask = new ScheduledTaskParameter
        {
            Id = 1,
            Name = "Failing Sync Job",
            JobExecutor = JobExecutor.SyncNewUnloadingTicket,
            IsEnabled = true,
            FrequencyInMinutes = 5,
            NextRunTime = DateTime.UtcNow.AddMinutes(-1),
            LastRunTime = null
        };

        var scheduledTasks = new List<ScheduledTaskParameter> { scheduledTask };

        _mockUnitOfWork.Setup(x => x.ScheduledTaskParameters.GetListAsync(
            It.IsAny<System.Linq.Expressions.Expression<Func<ScheduledTaskParameter, bool>>>(),
            It.IsAny<CancellationToken>()))
            .ReturnsAsync(scheduledTasks);

        _mockJobFactory.Setup(x => x.CreateJob(JobExecutor.SyncNewUnloadingTicket))
            .Returns(_mockJobExecutor.Object);

        _mockJobExecutor.Setup(x => x.ExecuteAsync(It.IsAny<CancellationToken>()))
            .ThrowsAsync(new InvalidOperationException("Job execution failed"));

        var scheduler = new MainSchedulerJob(_mockServiceProvider.Object, _mockLogger.Object);

        // Act
        using var cts = new CancellationTokenSource();
        cts.CancelAfter(TimeSpan.FromSeconds(1));

        try
        {
            await scheduler.StartAsync(cts.Token);
            await Task.Delay(100, cts.Token);
        }
        catch (OperationCanceledException)
        {
            // Expected
        }
        finally
        {
            await scheduler.StopAsync(CancellationToken.None);
        }

        // Assert
        _mockJobExecutor.Verify(x => x.ExecuteAsync(It.IsAny<CancellationToken>()), Times.AtLeastOnce);
        
        // Should still update times even when job fails
        _mockUnitOfWork.Verify(x => x.ScheduledTaskParameters.UpdateAsync(
            It.IsAny<ScheduledTaskParameter>(),
            It.IsAny<CancellationToken>()), Times.AtLeastOnce);
    }
}
