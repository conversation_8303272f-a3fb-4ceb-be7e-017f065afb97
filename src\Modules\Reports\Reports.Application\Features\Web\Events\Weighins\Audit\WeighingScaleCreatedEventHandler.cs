using MediatR;
using Reports.Domain;
using Reports.Domain.Constants;
using Reports.Domain.Entities;
using Reports.Domain.Events;

namespace Reports.Application.Features.Web.Events.Weighins.Audit;

public class WeighingScaleCreatedEventHandler : INotificationHandler<WeighingScaleCreatedEvent>
{
    private readonly IReportsUnitOfWork _unitOfWork;

    public WeighingScaleCreatedEventHandler(IReportsUnitOfWork unitOfWork)
    {
        _unitOfWork = unitOfWork;
    }

    public async Task Handle(WeighingScaleCreatedEvent notification, CancellationToken cancellationToken)
    {
        var auditRecords = HistoricalAudit
            .ForWeighingScales(notification.WeighingScales)
            .WithAction(HistoricalMovementType.Created)
            .ByUser(notification.User)
            .Build();

        await _unitOfWork.BeginTransactionAsync(cancellationToken);
        
        await _unitOfWork.HistoricalAudits.BulkInsertAsync(auditRecords, cancellationToken);
        
        await _unitOfWork.CommitAsync(cancellationToken);
    }
}
