using System.Linq.Expressions;
using EFCore.BulkExtensions;
using Microsoft.EntityFrameworkCore;
using Orion.SharedKernel.Application.Common.Services;
using Orion.SharedKernel.Infrastructure.Data.EFCore.ContextProvider;
using Orion.SharedKernel.Infrastructure.Data.EFCore.Repositories.Entities;
using Reports.Domain.Entities;
using Reports.Domain.Repositories;

namespace Reports.Infrastructure.Data.EFCore.Repositories;

public class UrbetrackSynchronizationRepository : Repository<UrbetrackSynchronization, int, ReportsDbContext>, IUrbetrackSynchronizationRepository
{
    public UrbetrackSynchronizationRepository(IDbContextProvider<ReportsDbContext> dbContextProvider, ICacheService cacheService) : base(dbContextProvider, cacheService) { }

    public async Task<UrbetrackSynchronization?> GetByConditionAsync(Expression<Func<UrbetrackSynchronization, bool>> predicate, CancellationToken cancellationToken)
    {
        return await _context.UrbetrackSynchronizations
            .Where(predicate)
            .FirstOrDefaultAsync(cancellationToken);
    }

    public async Task<List<UrbetrackSynchronization>> GetListAsync(IQueryable<UrbetrackSynchronization> query, CancellationToken cancellationToken)
    {
        return await query.ToListAsync(cancellationToken);
    }

    public IQueryable<UrbetrackSynchronization> GetQueryable()
    {
        return _context.UrbetrackSynchronizations.AsQueryable();
    }

    public async Task BulkInsertAsync(List<UrbetrackSynchronization> entities, CancellationToken cancellationToken)
    {
        await _context.BulkInsertAsync(entities,
            new BulkConfig
            {
                PreserveInsertOrder = false,
                SetOutputIdentity = false
            },
            cancellationToken: cancellationToken);
    }

    public async Task InsertAsync(UrbetrackSynchronization entity, CancellationToken cancellationToken)
    {
        await _context.UrbetrackSynchronizations.AddAsync(entity, cancellationToken);
        await _context.SaveChangesAsync(cancellationToken);
    }

    public async Task UpdateAsync(UrbetrackSynchronization entity, CancellationToken cancellationToken)
    {
        _context.UrbetrackSynchronizations.Update(entity);
        await _context.SaveChangesAsync(cancellationToken);
    }
}
